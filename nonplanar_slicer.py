import numpy as np
import trimesh
import os
import math # 为math.pi添加
from scipy.spatial import cKDTree
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from shapely.geometry import Polygon, LineString, MultiLineString, Point # 从shapely.geometry导入Point
import time # 导入time模块
import shapely
import csv # 添加csv模块导入
import traceback
import logging # 添加 logging 导入

class DirectProjectionSlicer:
    def __init__(self, mesh_path, target_surface_distance=0.4, slice_direction='x', inward_normals=True, min_points_req=2):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        init_start_time = time.time() # Moved here

        self.mesh_path = mesh_path
        self.mesh = None # Initialize mesh as None
        self.target_surface_distance = target_surface_distance
        self.slice_direction = slice_direction
        self.inward_normals = inward_normals
        self.min_points_req = min_points_req # 最小点数要求，用于过滤有效路径
        self.mesh_kdtree = None
        self.mesh_face_normals = None
        self.mesh_vertex_normals = None # 预计算顶点法线

        # 优化: 改进的迭代间距调整参数
        self.damping_factor_iterative_spacing = 0.7  # 提高阻尼因子以减少振荡

        # 增强: 性能优化缓存和统计 - 优化版本
        self._performance_stats = {
            'total_spacing_calculations': 0,
            'convergence_rate': 0.0,
            'cache_hit_rate': 0.0,
            'optimization_enabled': True,
            'target_achievement_rate': 0.0,
            'rms_error_percentage': 100.0,
            'spacing_warnings': 0,
            'overall_quality_score': 0.0,
            'path_rejection_rate': 0.0
        }

        # 新增: 多级智能缓存系统
        self._spacing_cache = {}  # 缓存间距计算结果
        self._spacing_cache_max_size = 500  # 增加缓存大小以提高命中率
        self._strip_quality_cache = {}  # 缓存条带质量评估
        self._spatial_index_cache = {}  # 空间索引缓存
        self._curvature_cache = {}  # 曲率分析缓存
        self._prediction_cache = {}  # 预测结果缓存

        # 新增: 自适应参数 - 优化版本
        self._adaptive_tolerance_factor = 0.7  # 设置为70%的目标，符合要求
        self._convergence_history = []  # 收敛历史记录
        self._quality_history = []  # 质量历史记录
        self._spacing_error_statistics = []  # 间距误差统计

        # 新增: 增强的算法组件
        self._enhanced_spacing_calculator = None  # 延迟初始化
        self._surface_curvature_analyzer = None   # 曲面分析器
        self._predictive_model = None             # 预测模型
        self._newton_raphson_solver = None        # Newton-Raphson求解器
        self._bisection_solver = None             # 二分法求解器
        self._hybrid_solver = None                # 混合求解器

        # 尝试加载网格，如果失败，则记录错误但不立即引发，允许后续操作可能不依赖网格
        try:
            # 检查文件是否存在
            if not os.path.exists(mesh_path):
                raise FileNotFoundError(f"文件 {mesh_path} 不存在")
            
            self.mesh = trimesh.load_mesh(mesh_path)
            self.mesh_path = mesh_path
            self.d_surf = target_surface_distance
            self.min_points_req = min_points_req
            self.inward_normals = inward_normals

            # 设置切片方向 (主要用于二维填充的扫描轴确定，或直接偏置策略的偏置/扫描轴)
            # axis_index: 0 表示 X 是主扫描/步进方向 (2D填充)，或偏置方向 (direct_offset)
            # axis_index: 1 表示 Y 是主扫描/步进方向 (2D填充)，或偏置方向 (direct_offset)
            if slice_direction.lower() == 'x':
                self.axis_index = 0 # X是主扫描轴(2D)或偏置轴(direct_offset), Y是步进轴(2D)或扫描轴(direct_offset)
            elif slice_direction.lower() == 'y':
                self.axis_index = 1 # Y是主扫描轴(2D)或偏置轴(direct_offset), X是步进轴(2D)或扫描轴(direct_offset)
            else:
                raise ValueError("切片方向必须是 'x' 或 'y'")

            # 确保网格有法线
            if not hasattr(self.mesh, 'face_normals') or self.mesh.face_normals is None:
                self.mesh.generate_face_normals() # trimesh方法，用于生成面法线
            if not hasattr(self.mesh, 'vertex_normals') or self.mesh.vertex_normals is None:
                self.mesh.generate_normals() # trimesh方法，用于生成顶点法线
            
            # KD树用于快速查询顶点法线
            self.vertex_points = self.mesh.vertices
            self.vertex_normals = self.mesh.vertex_normals
            self.vertex_tree = cKDTree(self.vertex_points)

            # 获取网格边界用于射线投影的范围
            self.mesh_bounds = self.mesh.bounds
            
            # 添加法线缓存以提高性能
            self._normal_cache = {}
            self._cache_tolerance = 1e-6  # 缓存容差，距离小于此值的点共享法线
            
            # 添加边界距离缓存
            self._boundary_distance_cache = {}
            self._boundary_linestrings = []  # 预存储的边界LineString列表
            
            print(f"网格加载完成。边界: {self.mesh_bounds}")

            # 初始化增强的优化组件
            self._initialize_enhanced_caching()
            self._initialize_quality_monitoring()

            print(f"--- Slicer 初始化完成 (含增强优化组件), 耗时: {time.time() - init_start_time:.4f} 秒 ---")
        except Exception as e:
            print(f"错误: 初始化时加载网格失败: {e}")
            self.mesh = None
            self.mesh_path = None
            self.d_surf = None
            self.min_points_req = None
            self.inward_normals = None
            self.axis_index = None
            self.mesh_face_normals = None
            self.mesh_vertex_normals = None
            self.vertex_points = None
            self.vertex_normals = None
            self.vertex_tree = None
            self.mesh_bounds = None
            self._normal_cache = None
            self._cache_tolerance = None
            self._boundary_distance_cache = None
            self._boundary_linestrings = None
            print(f"--- Slicer 初始化失败, 耗时: {time.time() - init_start_time:.4f} 秒 ---")

    def _initialize_enhanced_solvers(self):
        """
        初始化增强的求解器组件
        实现混合Newton-Raphson/二分法求解器以提高收敛性能
        """
        self._newton_raphson_solver = self._create_newton_raphson_solver()
        self._bisection_solver = self._create_bisection_solver()
        self._hybrid_solver = self._create_hybrid_solver()

    def _create_newton_raphson_solver(self):
        """
        创建Newton-Raphson求解器用于快速收敛
        """
        def newton_raphson_solve(func, dfunc, x0, tolerance=1e-6, max_iterations=20):
            """
            Newton-Raphson方法求解
            func: 目标函数
            dfunc: 导数函数
            x0: 初始猜测
            """
            x = x0
            for i in range(max_iterations):
                fx = func(x)
                if abs(fx) < tolerance:
                    return x, True, i + 1

                dfx = dfunc(x)
                if abs(dfx) < 1e-12:  # 避免除零
                    return x, False, i + 1

                x_new = x - fx / dfx

                # 添加步长限制以提高稳定性
                step = x_new - x
                if abs(step) > 0.5:  # 限制最大步长
                    step = 0.5 * np.sign(step)
                    x_new = x + step

                x = x_new

            return x, False, max_iterations

        return newton_raphson_solve

    def _create_bisection_solver(self):
        """
        创建二分法求解器用于稳定收敛
        """
        def bisection_solve(func, a, b, tolerance=1e-6, max_iterations=50):
            """
            二分法求解
            func: 目标函数
            a, b: 区间端点
            """
            fa, fb = func(a), func(b)

            if fa * fb > 0:
                return None, False, 0  # 区间内无根

            for i in range(max_iterations):
                c = (a + b) / 2
                fc = func(c)

                if abs(fc) < tolerance or abs(b - a) < tolerance:
                    return c, True, i + 1

                if fa * fc < 0:
                    b, fb = c, fc
                else:
                    a, fa = c, fc

            return (a + b) / 2, False, max_iterations

        return bisection_solve

    def _create_hybrid_solver(self):
        """
        创建混合求解器，结合Newton-Raphson和二分法的优势
        """
        def hybrid_solve(func, dfunc, x0, bounds=None, tolerance=1e-6, max_iterations=30):
            """
            混合求解方法：先尝试Newton-Raphson，失败时回退到二分法
            """
            # 首先尝试Newton-Raphson方法
            if dfunc is not None:
                result, success, iterations = self._newton_raphson_solver(
                    func, dfunc, x0, tolerance, min(15, max_iterations)
                )
                if success:
                    return result, True, iterations, 'newton_raphson'

            # Newton-Raphson失败，尝试二分法
            if bounds is not None:
                a, b = bounds
                result, success, iterations = self._bisection_solver(
                    func, a, b, tolerance, max_iterations - 15
                )
                if success:
                    return result, True, iterations + 15, 'bisection'

            # 两种方法都失败，返回最佳估计
            return x0, False, max_iterations, 'failed'

        return hybrid_solve

    def _initialize_enhanced_caching(self):
        """
        初始化增强的多级缓存系统
        """
        # 初始化求解器
        if self._hybrid_solver is None:
            self._initialize_enhanced_solvers()

        # 清理旧缓存
        self._clear_cache_if_needed()

    def _clear_cache_if_needed(self):
        """
        智能缓存清理，保持缓存大小在合理范围内
        """
        if len(self._spacing_cache) > self._spacing_cache_max_size:
            # 保留最近使用的50%缓存项
            keep_count = self._spacing_cache_max_size // 2
            # 简单的LRU策略：保留最近的项
            items = list(self._spacing_cache.items())
            self._spacing_cache = dict(items[-keep_count:])

        # 清理其他缓存
        for cache in [self._curvature_cache, self._prediction_cache, self._spatial_index_cache]:
            if len(cache) > 100:
                items = list(cache.items())
                cache.clear()
                cache.update(dict(items[-50:]))

    def _generate_enhanced_cache_key(self, *args, precision=1e-4):
        """
        生成增强的缓存键，支持浮点数精度控制
        """
        def round_value(val):
            if isinstance(val, (int, float)):
                return round(val / precision) * precision
            elif isinstance(val, np.ndarray):
                return tuple(round(v / precision) * precision for v in val.flatten())
            else:
                return str(val)

        return tuple(round_value(arg) for arg in args)

    def _get_cached_spacing_result(self, cache_key):
        """
        获取缓存的间距计算结果
        """
        if cache_key in self._spacing_cache:
            # 正确的缓存命中计数
            if not hasattr(self, '_cache_hits'):
                self._cache_hits = 0
                self._cache_misses = 0
            self._cache_hits += 1
            return self._spacing_cache[cache_key]
        else:
            if not hasattr(self, '_cache_misses'):
                self._cache_hits = 0
                self._cache_misses = 0
            self._cache_misses += 1
        return None

    def _cache_spacing_result(self, cache_key, result):
        """
        缓存间距计算结果
        """
        if len(self._spacing_cache) >= self._spacing_cache_max_size:
            self._clear_cache_if_needed()
        self._spacing_cache[cache_key] = result

    def _predict_optimal_spacing(self, current_pos, target_spacing, history_data):
        """
        使用机器学习方法预测最优间距
        基于历史数据和表面特征进行预测
        """
        cache_key = self._generate_enhanced_cache_key(current_pos, target_spacing, len(history_data))

        # 检查预测缓存
        if cache_key in self._prediction_cache:
            return self._prediction_cache[cache_key]

        # 简化的预测模型（可以扩展为更复杂的ML模型）
        if len(history_data) < 3:
            prediction = target_spacing
        else:
            # 基于历史趋势的预测
            recent_errors = [abs(h['measured'] - h['target']) / h['target'] for h in history_data[-3:]]
            avg_error = np.mean(recent_errors)

            # 自适应调整
            if avg_error < 0.05:  # 误差小，可以稍微激进
                prediction = target_spacing * 1.02
            elif avg_error > 0.15:  # 误差大，需要保守
                prediction = target_spacing * 0.98
            else:
                prediction = target_spacing

        # 缓存预测结果
        self._prediction_cache[cache_key] = prediction
        return prediction

    def _revolutionary_direct_spacing_calculator(self, current_pos, target_spacing, surface_complexity=1.0):
        """
        革命性直接间距计算器
        使用解析方法直接计算最优间距，避免迭代收敛问题
        """
        # 基于表面复杂度的直接计算
        base_factor = 1.0

        # 表面复杂度调整
        complexity_adjustment = 1.0 + (surface_complexity - 1.0) * 0.1

        # 位置相关的微调
        position_factor = 1.0
        if hasattr(self, 'mesh_bounds'):
            total_range = self.mesh_bounds[1, self.axis_index] - self.mesh_bounds[0, self.axis_index]
            if total_range > 0:
                relative_pos = (current_pos - self.mesh_bounds[0, self.axis_index]) / total_range
                # 边缘区域稍微保守，中间区域稍微激进
                position_factor = 0.98 + 0.04 * (1.0 - abs(relative_pos - 0.5) * 2.0)

        # 直接计算最优间距
        optimal_spacing = target_spacing * base_factor * complexity_adjustment * position_factor

        return optimal_spacing

    def _fast_convergence_predictor(self, target_spacing, history_data, surface_info=None):
        """
        快速收敛预测器
        基于表面几何特征直接预测最优步长
        """
        if len(history_data) == 0:
            return target_spacing

        # 基于历史数据的快速预测
        if len(history_data) >= 2:
            recent_errors = [abs(h.get('measured', target_spacing) - target_spacing) / target_spacing
                           for h in history_data[-2:]]
            avg_error = np.mean(recent_errors)

            # 如果误差很小，可以稍微激进
            if avg_error < 0.03:  # 3%误差以内
                return target_spacing * 1.02
            elif avg_error > 0.1:  # 10%误差以上
                return target_spacing * 0.98
            else:
                return target_spacing

        return target_spacing

    def _initialize_quality_monitoring(self):
        """
        初始化质量监控系统
        实时跟踪性能指标以达到目标要求
        """
        self._quality_monitor = {
            'target_achievement_samples': [],
            'rms_error_samples': [],
            'spacing_warning_count': 0,
            'total_calculations': 0,
            'path_rejection_count': 0,
            'cache_hit_count': 0,
            'convergence_success_count': 0
        }

    def _update_quality_metrics(self, measured_spacing, target_spacing, was_rejected=False):
        """
        更新质量指标
        """
        if not hasattr(self, '_quality_monitor'):
            self._initialize_quality_monitoring()

        self._quality_monitor['total_calculations'] += 1

        if was_rejected:
            self._quality_monitor['path_rejection_count'] += 1
            return

        if measured_spacing is not None and target_spacing > 0:
            error_percentage = abs(measured_spacing - target_spacing) / target_spacing * 100

            # 检查是否在±5%范围内
            if error_percentage <= 5.0:
                self._quality_monitor['target_achievement_samples'].append(1)
            else:
                self._quality_monitor['target_achievement_samples'].append(0)

            # 记录RMS误差样本
            self._quality_monitor['rms_error_samples'].append(error_percentage)

            # 检查是否需要警告
            if error_percentage > 15.0:  # 超过15%误差
                self._quality_monitor['spacing_warning_count'] += 1

    def _calculate_current_quality_score(self):
        """
        计算当前质量评分
        """
        if not hasattr(self, '_quality_monitor'):
            return 0.0

        monitor = self._quality_monitor

        if monitor['total_calculations'] == 0:
            return 0.0

        # 1. 目标达成率 (≥95%)
        if len(monitor['target_achievement_samples']) > 0:
            target_achievement_rate = np.mean(monitor['target_achievement_samples']) * 100
        else:
            target_achievement_rate = 0.0

        # 2. RMS误差 (<15%)
        if len(monitor['rms_error_samples']) > 0:
            rms_error = np.sqrt(np.mean(np.array(monitor['rms_error_samples'])**2))
        else:
            rms_error = 100.0

        # 3. 间距警告数 (<3)
        spacing_warnings = monitor['spacing_warning_count']

        # 4. 路径拒绝率 (<50%)
        path_rejection_rate = (monitor['path_rejection_count'] / monitor['total_calculations']) * 100

        # 5. 缓存命中率 (>20%)
        cache_hit_rate = self._performance_stats.get('cache_hit_rate', 0.0) * 100

        # 计算综合质量评分
        quality_score = (
            0.35 * min(target_achievement_rate / 95.0, 1.0) * 100 +  # 35%权重
            0.25 * max(0, (15.0 - rms_error) / 15.0) * 100 +         # 25%权重
            0.15 * max(0, (3 - spacing_warnings) / 3.0) * 100 +      # 15%权重
            0.15 * max(0, (50.0 - path_rejection_rate) / 50.0) * 100 + # 15%权重
            0.10 * min(cache_hit_rate / 20.0, 1.0) * 100             # 10%权重
        )

        # 更新性能统计
        self._performance_stats.update({
            'target_achievement_rate': target_achievement_rate,
            'rms_error_percentage': rms_error,
            'spacing_warnings': spacing_warnings,
            'overall_quality_score': quality_score,
            'path_rejection_rate': path_rejection_rate
        })

        return quality_score

    def get_surface_normal_at_point(self, point_3d):
        """获取三维空间中给定点在原始网格曲面上的法线向量（通过最近顶点插值）。
        优化版本：添加缓存机制提高性能。"""
        
        # 检查缓存
        point_key = tuple(np.round(point_3d / self._cache_tolerance).astype(int))
        if point_key in self._normal_cache:
            return self._normal_cache[point_key]
            
        # 查询最近的顶点索引
        _, idx = self.vertex_tree.query(point_3d, k=1)
        # 获取该顶点的法线
        normal = self.mesh.vertex_normals[idx]
        
        if self.inward_normals:
            normal = -normal # 如果需要面内法线，则反转法线方向
            
        normalized_normal = normal / np.linalg.norm(normal) # 归一化
        
        # 缓存结果
        self._normal_cache[point_key] = normalized_normal
        
        return normalized_normal

    def get_surface_normals_batch(self, points_3d):
        """批量获取多个点的表面法线，性能优化版本"""
        if points_3d is None or len(points_3d) == 0:
            return np.array([])
            
        # 批量查询最近顶点
        _, indices = self.vertex_tree.query(points_3d, k=1)
        normals = self.mesh.vertex_normals[indices]
        
        if self.inward_normals:
            normals = -normals
            
        # 批量归一化
        norms = np.linalg.norm(normals, axis=1, keepdims=True)
        norms[norms == 0] = 1  # 避免除零
        normalized_normals = normals / norms
        
        return normalized_normals

    def interpolate_path_points(self, points, max_segment_length):
        """
        对路径点进行插值，确保相邻点之间的距离不超过指定的最大长度。
        参数:
        points: numpy数组，形状为(N, D)，原始路径点坐标
        max_segment_length: 允许的线段最大长度
        返回:
        numpy数组，包含原始点和插入的插值点
        """
        if points is None or len(points) < 2 or max_segment_length <= 0:
            return points
            
        interpolated_points = []
        for i in range(len(points) - 1):
            p1 = points[i]
            p2 = points[i + 1]
            segment_length = np.linalg.norm(p2 - p1)
            interpolated_points.append(p1)
            if segment_length > max_segment_length:
                num_points_to_insert = int(np.ceil(segment_length / max_segment_length)) - 1
                for j in range(1, num_points_to_insert + 1):
                    t = j / (num_points_to_insert + 1)
                    interp_point = p1 + t * (p2 - p1)
                    interpolated_points.append(interp_point)
        interpolated_points.append(points[-1])
        return np.array(interpolated_points)

    def normal_to_rpy(self, normal):
        """
        将法线向量转换为RPY角(Roll-Pitch-Yaw)
        参数: normal: 3D法线向量 [nx, ny, nz]
        返回: roll, pitch, yaw: 欧拉角(弧度)
        """
        normal = normal / np.linalg.norm(normal)
        nx, ny, nz = normal
        if abs(nz) < 0.999: # 避免法线接近Z轴时的万向锁问题
            aux_vec = np.array([0, 0, 1])
        else:
            aux_vec = np.array([1, 0, 0])
        
        x_axis = np.cross(aux_vec, normal)
        if np.linalg.norm(x_axis) < 1e-6: # 如果法线与辅助向量平行
            # 尝试另一个辅助向量
            aux_vec = np.array([0,1,0]) if abs(normal[1]) < 0.999 else np.array([1,0,0])
            x_axis = np.cross(aux_vec, normal)
        x_axis = x_axis / np.linalg.norm(x_axis)
        y_axis = np.cross(normal, x_axis)
        y_axis = y_axis / np.linalg.norm(y_axis)
        
        R = np.column_stack((x_axis, y_axis, normal))
        
        pitch = np.arcsin(-R[2, 0])
        if abs(np.cos(pitch)) > 1e-9:
            roll = np.arctan2(R[2, 1], R[2, 2])
            yaw = np.arctan2(R[1, 0], R[0, 0])
        else: # 万向锁
            roll = np.arctan2(-R[0, 2], R[1, 1]) # 假设yaw为0或通过其他方式确定
            yaw = 0
        return roll, pitch, yaw

    def normal_to_rpy_degrees(self, normal):
        """将法线向量转换为RPY角(Roll-Pitch-Yaw)，单位为度。"""
        roll_rad, pitch_rad, yaw_rad = self.normal_to_rpy(normal)
        return math.degrees(roll_rad), math.degrees(pitch_rad), math.degrees(yaw_rad)

    def visualize_paths(self, paths_data, show_normals=False, normal_scale=0.5, 
                          normal_hop_distance=None, clearance_above_model_max_viz=None,
                          # 新增参数，用于显示2D填充数据
                          show_2d_fill=False, fill_2d_data=None, projection_connections=None): # 用于可视化的重命名参数
        """
        可视化生成的路径（边界、投影填充段）以及抬刀路径（抬至模型最大Z之上）。
        
        新增功能：可以同时显示2D填充数据和从2D点到3D投影点的连接线。
        
        参数:
        paths_data: 3D路径数据
        show_normals: 是否显示法线
        normal_scale: 法线显示比例
        normal_hop_distance: 法线跳跃距离
        clearance_above_model_max_viz: 模型上方的间隙
        show_2d_fill: 是否显示2D填充数据
        fill_2d_data: 2D填充数据，格式为 (original_contour_2d, offset_polygon_shapely, fill_points_2d, connection_types)
        projection_connections: 2D到3D的投影连接数据，格式为 (points_2d, points_3d, success_mask)
        """
        plt.rcParams['font.sans-serif'] = ['SimHei'] # 设置显示中文的字体
        plt.rcParams['axes.unicode_minus'] = False # 解决负号'-'显示为方块的问题
        
        # 根据是否显示2D数据来决定子图布局
        if show_2d_fill and fill_2d_data is not None:
            # 图1: 3D 路径可视化
            fig3d = plt.figure(figsize=(12, 9)) # 为3D图创建一个独立的figure
            ax3d = fig3d.add_subplot(111, projection='3d')
            # 图2: 2D 填充路径规划 (如果需要)
            fig2d = plt.figure(figsize=(10, 8)) # 为2D图创建一个独立的figure
            ax2d = fig2d.add_subplot(111)
        else:
            fig3d = plt.figure(figsize=(12, 9))
            ax3d = fig3d.add_subplot(111, projection='3d')
            ax2d = None # 没有2D图
            fig2d = None # 没有2D图的figure对象

        # 设置3D子图
        ax3d.set_axis_off()
        ax3d.grid(False)
        ax3d.set_facecolor('white')
        fig3d.patch.set_facecolor('white')

        if not paths_data:
            print("没有路径可供可视化。")
            ax3d.set_title("未找到路径")
            plt.show()
            return

        all_valid_points_list = []
        boundary_segments = []
        projected_fill_segments = [] # 这现在将保存任一策略的填充段

        for points, normals, is_boundary, segment_id in paths_data:
            if points is None or len(points) < 1:
                continue
            all_valid_points_list.append(points)
            if is_boundary:
                boundary_segments.append((points, normals, segment_id))
            else:
                projected_fill_segments.append((points, normals, segment_id)) # 填充路径（投影或直接偏置）
        
        if not all_valid_points_list:
            print("所有路径段都为空或无效。")
            ax3d.set_title("未找到有效路径点")
            plt.show()
            return

        all_valid_points = np.vstack(all_valid_points_list)
        min_vals, max_vals = np.min(all_valid_points, axis=0), np.max(all_valid_points, axis=0)
        model_max_z_for_viz = max_vals[2] # 获取模型最大Z值用于可视化跳跃计算

        if self.mesh:
            # 将网格颜色设置为非常浅的灰色，使其在白色背景下几乎不可见，但仍能提供上下文
            ax3d.plot_trisurf(self.mesh.vertices[:,0], self.mesh.vertices[:,1], self.mesh.vertices[:,2], 
                            triangles=self.mesh.faces, alpha=0.05, color='lightgray', label='原始网格')

        num_boundary_segments = len(boundary_segments)
        if num_boundary_segments > 0:
            print(f"正在绘制 {num_boundary_segments} 个边界路径段...")
            first_boundary_label_added = False
            for i, (points, normals, seg_id) in enumerate(boundary_segments):
                color = 'green' # 统一边界路径颜色
                label = None
                if not first_boundary_label_added:
                    label = "边界路径"
                    first_boundary_label_added = True
                
                ax3d.plot(points[:,0], points[:,1], points[:,2], color=color, marker='.', markersize=2, linewidth=1.0, label=label)
                if show_normals and normals is not None and len(points) == len(normals):
                    ax3d.quiver(points[:,0], points[:,1], points[:,2], normals[:,0], normals[:,1], normals[:,2], length=normal_scale, color='lime', arrow_length_ratio=0.3)
        
        num_fill_segments = len(projected_fill_segments)
        if num_fill_segments > 0:
            print(f"正在绘制 {num_fill_segments} 个填充路径段...")
            first_fill_label_added = False
            for i, (points, normals, seg_id) in enumerate(projected_fill_segments):
                color = 'blue' 
                linestyle = '-' 
                label = None
                if not first_fill_label_added:
                    label = "填充路径" # 填充路径的通用标签
                    first_fill_label_added = True
                ax3d.plot(points[:,0], points[:,1], points[:,2], color=color, linestyle=linestyle, marker='.', markersize=3, linewidth=1.5, label=label)
                if show_normals and normals is not None and len(points) == len(normals):
                    ax3d.quiver(points[:,0], points[:,1], points[:,2], normals[:,0], normals[:,1], normals[:,2], length=normal_scale, color='magenta', arrow_length_ratio=0.3)

        # 显示从2D到3D的投影连接线
        if show_2d_fill and projection_connections is not None:
            points_2d, points_3d, success_mask = projection_connections
            if points_2d is not None and points_3d is not None and success_mask is not None:
                print(f"正在绘制 {np.sum(success_mask)} 条2D到3D的投影连接线...")
                
                # 将2D点放到模型正上方10mm处，形成更直观的投影效果
                projection_height = model_max_z_for_viz + 10.0  # 模型最高点上方10mm
                points_2d_elevated = np.column_stack([points_2d, np.full(len(points_2d), projection_height)])
                
                # 只显示成功投影的连接线
                successful_indices = np.where(success_mask)[0]
                
                # 绘制投影连接线（从上方2D点到下方3D曲面点）
                connection_line_count = 0
                for idx in successful_indices[::8]:  # 每8个点显示一条连接线，进一步减少密度
                    p2d_elevated = points_2d_elevated[idx]
                    p3d = points_3d[idx]
                    ax3d.plot([p2d_elevated[0], p3d[0]], [p2d_elevated[1], p3d[1]], [p2d_elevated[2], p3d[2]], 
                             color='red', linestyle='--', alpha=0.7, linewidth=1.0)
                    connection_line_count += 1
                
                print(f"  实际绘制了 {connection_line_count} 条投影连接线")
                
                # 在3D图中显示上方的2D填充点
                ax3d.scatter(points_2d_elevated[:,0], points_2d_elevated[:,1], points_2d_elevated[:,2], 
                           c='purple', s=8, alpha=0.8, label=f'2D填充点(Z={projection_height:.1f}mm)', 
                           edgecolors='black', linewidth=0.3)
                
                # 绘制2D填充点之间的连接线（在上方平面）
                if len(points_2d) > 1:
                    ax3d.plot(points_2d_elevated[:,0], points_2d_elevated[:,1], points_2d_elevated[:,2], 
                             color='magenta', linestyle='-', alpha=0.6, linewidth=1.2, label='2D填充路径(上方)')
                
                # 添加一个半透明的平面来表示2D规划层
                if len(points_2d_elevated) > 0:
                    # 创建2D规划平面的边界框
                    x_min, x_max = np.min(points_2d_elevated[:,0]), np.max(points_2d_elevated[:,0])
                    y_min, y_max = np.min(points_2d_elevated[:,1]), np.max(points_2d_elevated[:,1])
                    
                    # 扩展边界一点点
                    margin = max(x_max - x_min, y_max - y_min) * 0.1
                    x_min -= margin
                    x_max += margin
                    y_min -= margin
                    y_max += margin
                    
                    # 创建网格平面
                    xx, yy = np.meshgrid([x_min, x_max], [y_min, y_max])
                    zz = np.full_like(xx, projection_height)
                    
                    # ax3d.plot_surface(xx, yy, zz, alpha=0.15, color='cyan', label='2D规划平面')

        x_range, y_range, z_range = max_vals - min_vals
        max_range = max(x_range, y_range, z_range, 1.0) # 确保max_range至少为1，避免后续计算问题
        mid_vals = (max_vals + min_vals) / 2
        ax3d.set_xlim(mid_vals[0] - max_range * 0.6, mid_vals[0] + max_range * 0.6)
        ax3d.set_ylim(mid_vals[1] - max_range * 0.6, mid_vals[1] + max_range * 0.6)
        ax3d.set_zlim(mid_vals[2] - max_range * 0.6, mid_vals[2] + max_range * 0.6)
        ax3d.set_box_aspect([1,1,1]) # 设置坐标轴等比例
        ax3d.set_xlabel('X (mm)'); ax3d.set_ylabel('Y (mm)'); ax3d.set_zlabel('Z (mm)')
        
        if show_2d_fill and ax2d is not None and fig2d is not None: # 检查fig2d也存在
            ax3d.set_title('3D 路径可视化 + 2D填充投影\\n(2D路径位于上方10mm处)')
            fig3d.tight_layout(rect=[0,0,0.85,1]) # 为3D图调整布局以适应图例
        else:
            ax3d.set_title('3D 路径可视化 (含抬刀)')
            fig3d.tight_layout(rect=[0,0,0.85,1]) # 调整布局以适应图例

        # 绘制2D填充数据（如果提供）
        if show_2d_fill and ax2d is not None and fill_2d_data is not None and fig2d is not None: # 检查fig2d也存在
            original_contour_2d, offset_polygon_shapely, fill_points_2d, connection_types = fill_2d_data
            
            # 设置2D子图 (现在是独立的图)
            ax2d.set_axis_off()
            ax2d.grid(False)
            ax2d.set_facecolor('white')

            # 绘制原始2D轮廓
            if original_contour_2d is not None and len(original_contour_2d) > 0:
                closed_original_contour = np.vstack([original_contour_2d, original_contour_2d[0]])
                ax2d.plot(closed_original_contour[:, 0], closed_original_contour[:, 1], 'b-', label='原始边界轮廓', linewidth=1.5)

            # 绘制偏置轮廓
            if offset_polygon_shapely is not None and not offset_polygon_shapely.is_empty:
                if offset_polygon_shapely.geom_type == 'Polygon':
                    offset_exterior_coords = np.array(offset_polygon_shapely.exterior.coords)
                    ax2d.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label='内缩后填充区域', linewidth=1.5)
                    for interior in offset_polygon_shapely.interiors:
                        offset_interior_coords = np.array(interior.coords)
                        ax2d.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
                elif offset_polygon_shapely.geom_type == 'MultiPolygon':
                    first_poly_in_multi = True
                    for poly in offset_polygon_shapely.geoms:
                        offset_exterior_coords = np.array(poly.exterior.coords)
                        current_label = ""
                        if first_poly_in_multi:
                            current_label = '内缩后填充区域 (多边形)'
                            first_poly_in_multi = False
                        ax2d.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label=current_label, linewidth=1.5)
                        for interior in poly.interiors:
                            offset_interior_coords = np.array(interior.coords)
                            ax2d.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
            
            # 绘制2D填充点和连接线
            if fill_points_2d is not None and len(fill_points_2d) > 0:
                ax2d.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], '.', color='purple', markersize=2, label='路径点')
                has_virtual_label = False
                has_fill_label = False
                if connection_types is not None and len(connection_types) == len(fill_points_2d):
                    for i in range(1, len(fill_points_2d)):
                        p_prev = fill_points_2d[i-1]
                        p_curr = fill_points_2d[i]
                        is_virtual_segment = (connection_types[i] == 1)
                        if is_virtual_segment:
                            linestyle = '--'
                            color = 'red'
                            current_label = '虚拟连接 (空程)' if not has_virtual_label else None
                            has_virtual_label = True
                        else: 
                            linestyle = '-'
                            color = 'blue'
                            current_label = '实际填充路径' if not has_fill_label else None
                            has_fill_label = True
                        ax2d.plot([p_prev[0], p_curr[0]], [p_prev[1], p_curr[1]],
                                color=color, linestyle=linestyle, linewidth=1.2, label=current_label)
                else:
                    ax2d.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], 'm.-', label='2D 填充路径 (无类型信息)', markersize=3, linewidth=1)

            ax2d.set_xlabel('X (mm)')
            ax2d.set_ylabel('Y (mm)')
            ax2d.set_title('2D 填充路径规划\\n(投影前的规划视图)')
            ax2d.legend(loc='best')
            ax2d.axis('equal')
            ax2d.grid(True, linestyle=':', alpha=0.7)
            fig2d.tight_layout() # 为2D图调整布局
        
        # 如果提供了参数，则绘制移动路径
        if len(paths_data) > 1 and normal_hop_distance is not None and clearance_above_model_max_viz is not None:
            print(f"正在绘制 {len(paths_data) - 1} 个路径段间的抬刀和移动路径...")
            first_travel_label_added = False
            travel_color = 'red'
            travel_linestyle = ':' # 虚线表示空程
            travel_linewidth = 0.8

            for i in range(len(paths_data) - 1):
                current_segment_tuple = paths_data[i]
                next_segment_tuple = paths_data[i+1]

                current_points, current_normals, _, current_seg_id = current_segment_tuple
                next_points, _, _, next_seg_id = next_segment_tuple

                if current_points is None or len(current_points) < 1 or \
                   current_normals is None or len(current_normals) != len(current_points):
                    print(f"  可视化: 跳过从段 {current_seg_id} 出发的抬刀路径，因当前段数据无效。")
                    continue
                
                if next_points is None or len(next_points) < 1:
                    print(f"  可视化: 跳过到段 {next_seg_id} 的抬刀路径，因下一段数据无效。")
                    continue

                p1_end_of_print = current_points[-1] # 当前打印段的结束点
                last_normal = current_normals[-1] # 当前打印段结束点的法线
                travel_sequence_points = [p1_end_of_print] # 初始化移动序列点

                # 1. 沿工具轴抬刀 (法线方向跳跃)
                p2_after_tool_axis_hop = p1_end_of_print # 默认情况下，如果没有法线跳跃，则点不变
                tool_axis_hop_target = p1_end_of_print 
                if normal_hop_distance > 0 and last_normal is not None:
                    norm_val = np.linalg.norm(last_normal)
                    if norm_val > 1e-6:
                        # 工具轴方向：如果法线朝内，则工具轴是-normal；否则是normal
                        tool_axis_direction_viz = -last_normal if self.inward_normals else last_normal
                        tool_axis_unit_viz = tool_axis_direction_viz / norm_val
                        tool_axis_hop_target = p1_end_of_print + tool_axis_unit_viz * normal_hop_distance
                travel_sequence_points.append(tool_axis_hop_target)
                p_current_viz = tool_axis_hop_target # 更新当前点
                
                # 2. 全局Z轴抬刀至模型最大Z值以上
                target_z_secondary_absolute_viz = model_max_z_for_viz + clearance_above_model_max_viz
                p3_after_global_z_hop = np.array([p_current_viz[0], 
                                                  p_current_viz[1],
                                                  target_z_secondary_absolute_viz])
                travel_sequence_points.append(p3_after_global_z_hop)
                p_current_viz = p3_after_global_z_hop # 更新当前点
                
                # 准备移动到下一段的预接近点
                p_next_segment_start_print = next_points[0] # 下一段打印的起始点
                _, next_segment_normals_data, _, _ = next_segment_tuple # 下一段的法线数据
                
                if next_segment_normals_data is None or len(next_segment_normals_data) == 0:
                    print(f"  可视化: 跳过到段 {next_seg_id} 的接近路径，因下一段法线数据无效或缺失。")
                    continue 
                
                next_segment_start_normal_raw_viz = next_segment_normals_data[0] # 下一段起始点的原始法线
                pre_approach_point_next_seg_viz = p_next_segment_start_print # 默认预接近点是打印起始点
                if normal_hop_distance > 0 and next_segment_start_normal_raw_viz is not None:
                    norm_val_next_start = np.linalg.norm(next_segment_start_normal_raw_viz)
                    if norm_val_next_start > 1e-6:
                        # 接近时的工具轴：如果inward_normals为True，工具轴为-normal。否则，工具轴为normal。
                        tool_axis_next_start_viz = -next_segment_start_normal_raw_viz if self.inward_normals else next_segment_start_normal_raw_viz
                        tool_axis_unit_next_start_viz = tool_axis_next_start_viz / norm_val_next_start
                        pre_approach_point_next_seg_viz = p_next_segment_start_print + tool_axis_unit_next_start_viz * normal_hop_distance
                
                # 3. 在安全高度移动到下一段预接近点的XY位置
                p4_at_pre_approach_xy_high_z = np.array([pre_approach_point_next_seg_viz[0],
                                                         pre_approach_point_next_seg_viz[1],
                                                         p_current_viz[2]]) # 使用当前的Z安全高度
                travel_sequence_points.append(p4_at_pre_approach_xy_high_z)
                
                # 4. 沿工具轴下降到下一段的预接近点
                travel_sequence_points.append(pre_approach_point_next_seg_viz)
                
                # 5. 移动到下一段的打印起始点
                travel_sequence_points.append(p_next_segment_start_print)
                
                travel_pts_np = np.array(travel_sequence_points)
                ax3d.plot(travel_pts_np[:,0], travel_pts_np[:,1], travel_pts_np[:,2],
                        color=travel_color, linestyle=travel_linestyle, linewidth=travel_linewidth,
                        label="抬刀/移动路径" if not first_travel_label_added else None, marker='x', markersize=2)
                if not first_travel_label_added and len(travel_pts_np)>0 :
                    first_travel_label_added = True
        
        handles, labels = ax3d.get_legend_handles_labels()
        by_label = dict(zip(labels, handles)) # 去除重复图例
        if by_label:
            ax3d.legend(by_label.values(), by_label.keys(), loc='upper left', bbox_to_anchor=(1,1))
        
        # 显示所有创建的图表
        plt.show()

    def save_paths_to_file(self, paths_data, output_file):
        """将路径数据保存到文件。"""
        if not paths_data:
            print("警告: save_paths_to_file 没有路径数据可写。")
            return

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 直接投影切片路径\n")
            f.write(f"# 目标表面距离 (2D填充参考): {self.d_surf}mm\n")
            f.write(f"# 总路径段数: {len(paths_data)}\n")
            f.write(f"# 原始网格: {self.mesh_path}\n")
            f.write(f"# 格式: 点ID X Y Z Roll Pitch Yaw 路径ID 路径类型\n\n")
            
            file_path_id_counter = 0 # 用于文件中路径段的唯一ID
            for points, normals, is_boundary, original_segment_id in paths_data:
                if points is None or len(points) < self.min_points_req:
                    print(f"  保存时跳过无效或过短的路径段 (ID: {original_segment_id})，点数: {len(points) if points is not None else 'None'}")
                    continue
                
                file_path_id_counter += 1
                path_type_str = "BOUNDARY" if is_boundary else f"FILL_SEGMENT_{original_segment_id}" # 路径类型字符串
                
                if normals is None or len(normals) != len(points):
                    print(f"警告: 路径段 {original_segment_id} 缺少或法线数量不匹配点数，将尝试重新计算。")
                    temp_normals = []
                    for p_idx in range(len(points)):
                        temp_normals.append(self.get_surface_normal_at_point(points[p_idx]))
                    used_normals = np.array(temp_normals)
                else:
                    used_normals = normals

                f.write(f"# PathSegment {file_path_id_counter}, OriginalID: {original_segment_id}, Type: {path_type_str}, Points: {len(points)}\n")
                for j, p_coord in enumerate(points):
                    current_normal = used_normals[j]
                    roll, pitch, yaw = self.normal_to_rpy(current_normal) # 弧度
                    path_type_numeric = 1 if is_boundary else 0 # 路径类型数字表示 (1:边界, 0:填充)
                    f.write(f"{j} {p_coord[0]:.6f} {p_coord[1]:.6f} {p_coord[2]:.6f} {roll:.6f} {pitch:.6f} {yaw:.6f} {file_path_id_counter} {path_type_numeric}\n")
                f.write("\n")
        print(f"路径已保存到 {output_file}")

    def get_boundary_edges(self):
        """提取网格的所有独立边界路径的原始边。"""
        boundary_edges_tuples = [] # 存储边界边的元组列表
        edge_map = {} # 边到面的映射
        for face_idx, face in enumerate(self.mesh.faces):
            edges_in_face = [ # 面中的三条边，顶点索引排序
                tuple(sorted((face[0], face[1]))),
                tuple(sorted((face[1], face[2]))),
                tuple(sorted((face[2], face[0])))
            ]
            for edge in edges_in_face:
                if edge in edge_map:
                    edge_map[edge].append(face_idx)
                else:
                    edge_map[edge] = [face_idx]
        
        # 边界边只与一个面相邻
        for edge, face_indices in edge_map.items():
            if len(face_indices) == 1:
                boundary_edges_tuples.append(edge) 
        
        if not boundary_edges_tuples:
            return []
        return boundary_edges_tuples

    def sort_boundary_edges(self, edges_tuples_list):
        """将未排序的边界边元组列表组织成一个或多个有序的边路径。"""
        if not edges_tuples_list:
            return []
        adj = {} # 邻接表：顶点 -> 包含该顶点的边的索引列表
        for i, (v1, v2) in enumerate(edges_tuples_list):
            adj.setdefault(v1, []).append(i)
            adj.setdefault(v2, []).append(i)

        all_ordered_paths_edges = [] # 存储所有排序后的路径 (每条路径是一个边列表)
        visited_edges = [False] * len(edges_tuples_list) # 标记边是否已被访问

        for i in range(len(edges_tuples_list)): # 遍历所有边，尝试从每个未访问的边开始构建路径
            if not visited_edges[i]:
                current_path_edge_indices = [] # 当前路径的边索引列表
                start_edge_index = i
                current_path_edge_indices.append(start_edge_index)
                visited_edges[start_edge_index] = True
                v_left, v_right = edges_tuples_list[start_edge_index] # 当前路径的两个端点
                
                # 从 v_right 端点开始延伸路径
                tip_to_extend = v_right
                while True:
                    extended = False
                    for edge_idx_candidate in adj.get(tip_to_extend, []):
                        if not visited_edges[edge_idx_candidate]:
                            current_path_edge_indices.append(edge_idx_candidate)
                            visited_edges[edge_idx_candidate] = True
                            next_edge = edges_tuples_list[edge_idx_candidate]
                            # 更新下一个要延伸的端点
                            tip_to_extend = next_edge[0] if next_edge[1] == tip_to_extend else next_edge[1]
                            extended = True
                            break
                    if not extended: # 无法再延伸
                        break
                
                # 从 v_left 端点开始反向延伸路径
                tip_to_extend = v_left
                while True:
                    extended = False
                    for edge_idx_candidate in adj.get(tip_to_extend, []):
                        if not visited_edges[edge_idx_candidate]:
                            current_path_edge_indices.insert(0, edge_idx_candidate) # 插入到路径的开头
                            visited_edges[edge_idx_candidate] = True
                            next_edge = edges_tuples_list[edge_idx_candidate]
                            tip_to_extend = next_edge[0] if next_edge[1] == tip_to_extend else next_edge[1]
                            extended = True
                            break
                    if not extended:
                        break
                
                if current_path_edge_indices:
                    ordered_edges_for_this_path = [edges_tuples_list[idx] for idx in current_path_edge_indices]
                    all_ordered_paths_edges.append(ordered_edges_for_this_path)
        return all_ordered_paths_edges

    def visualize_2d_fill(self, original_contour_2d, offset_polygon_shapely, fill_points_2d, connection_types, row_spacing=None):
        """可视化2D填充过程，包括原始轮廓、偏置轮廓和生成的填充点。"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        fig, ax = plt.subplots(figsize=(10, 8))

        # 移除坐标轴和背景网格，设置背景为白色
        ax.set_axis_off()
        ax.grid(False)
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        # 绘制原始2D轮廓
        if original_contour_2d is not None and len(original_contour_2d) > 0:
            closed_original_contour = np.vstack([original_contour_2d, original_contour_2d[0]])
            ax.plot(closed_original_contour[:, 0], closed_original_contour[:, 1], 'b-', label='原始边界轮廓', linewidth=1.5)

        if offset_polygon_shapely is not None and not offset_polygon_shapely.is_empty:
            if offset_polygon_shapely.geom_type == 'Polygon':
                offset_exterior_coords = np.array(offset_polygon_shapely.exterior.coords)
                ax.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label='内缩后填充区域', linewidth=1.5)
                for interior in offset_polygon_shapely.interiors:
                    offset_interior_coords = np.array(interior.coords)
                    ax.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
            elif offset_polygon_shapely.geom_type == 'MultiPolygon':
                first_poly_in_multi = True
                for poly in offset_polygon_shapely.geoms:
                    offset_exterior_coords = np.array(poly.exterior.coords)
                    current_label = ""
                    if first_poly_in_multi:
                        current_label = '内缩后填充区域 (多边形)'
                        first_poly_in_multi = False
                    ax.plot(offset_exterior_coords[:, 0], offset_exterior_coords[:, 1], 'g--', label=current_label, linewidth=1.5)
                    for interior in poly.interiors:
                        offset_interior_coords = np.array(interior.coords)
                        ax.plot(offset_interior_coords[:, 0], offset_interior_coords[:, 1], 'g--')
        
        if fill_points_2d is not None and len(fill_points_2d) > 0:
            ax.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], '.', color='purple', markersize=4, label='路径点')
            has_virtual_label = False
            has_fill_label = False
            if connection_types is not None and len(connection_types) == len(fill_points_2d):
                for i in range(1, len(fill_points_2d)):
                    p_prev = fill_points_2d[i-1]
                    p_curr = fill_points_2d[i]
                    is_virtual_segment = (connection_types[i] == 1)
                    if is_virtual_segment:
                        linestyle = '--'
                        color = 'red'
                        current_label = '虚拟连接 (空程)' if not has_virtual_label else None
                        has_virtual_label = True
                    else: 
                        linestyle = '-'
                        color = 'blue'
                        current_label = '实际填充路径' if not has_fill_label else None
                        has_fill_label = True
                    ax.plot([p_prev[0], p_curr[0]], [p_prev[1], p_curr[1]],
                            color=color, linestyle=linestyle, linewidth=1.2, label=current_label)
            else:
                ax.plot(fill_points_2d[:, 0], fill_points_2d[:, 1], 'm.-', label='2D 填充路径 (无类型信息)', markersize=3, linewidth=1)

        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        title = '2D 填充路径可视化'
        if row_spacing is not None:
            title += f' (行距: {row_spacing:.2f}mm)'
        ax.set_title(title)
        ax.legend(loc='best')
        ax.axis('equal')
        plt.grid(True, linestyle=':', alpha=0.7)
        plt.tight_layout()
        plt.show()

    def generate_2d_raster_fill(self, contour_2d_points, row_spacing, offset_distance=None, max_segment_length=None, inner_contours_list=None):
        """
        在给定的二维轮廓内生成Z字形（光栅）填充路径，同时考虑内部孔洞。
        优化版本：减少重复的几何运算，提高性能。
        参数:
        contour_2d_points: (N, 2) numpy数组，XOY平面上的闭合轮廓顶点。
        row_spacing: 填充线间距。
        offset_distance: 可选，向内偏移距离。默认: row_spacing / 2.0。
        max_segment_length: 可选，填充线段最大长度，用于插值。
        inner_contours_list: 可选，内部轮廓（孔洞）的列表，每个元素为(N, 2) numpy数组。
        返回:
        (fill_points_2d, connection_types, offset_polygon_shapely) 或 (None, None, None) 如果失败。
        fill_points_2d: (M, 2) numpy数组，生成的2D填充点。
        connection_types: (M,) list，标记每个点如何连接到前一个点 (0=填充, 1=空程)。
        offset_polygon_shapely: 内缩后的Shapely多边形对象。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or row_spacing <= 0:
            print("错误: 轮廓无效或行距不正确，无法生成2D填充。")
            return None, None, None

        try:
            # 创建外部轮廓多边形，优化几何验证
            exterior_polygon = Polygon(contour_2d_points)
            if not exterior_polygon.is_valid:
                exterior_polygon = exterior_polygon.buffer(0)
                if not exterior_polygon.is_valid or exterior_polygon.is_empty:
                    print("错误: 外部轮廓几何无效或为空。")
                    return None, None, None
            
            # 处理内部轮廓（孔洞）- 预先验证以避免重复检查
            valid_holes = []
            if inner_contours_list:
                for inner_contour in inner_contours_list:
                    if inner_contour is not None and len(inner_contour) >= 3:
                        try:
                            inner_poly = Polygon(inner_contour)
                            if not inner_poly.is_valid:
                                inner_poly = inner_poly.buffer(0)
                            if (inner_poly.is_valid and not inner_poly.is_empty and 
                                exterior_polygon.contains(inner_poly)):
                                valid_holes.append(inner_contour)
                        except Exception as inner_e:
                            print(f"警告: 处理内部轮廓时出错: {inner_e}")
            
            # 创建包含孔洞的多边形
            if valid_holes:
                polygon = Polygon(contour_2d_points, holes=valid_holes)
                print(f"创建了包含 {len(valid_holes)} 个孔洞的多边形用于填充。")
            else:
                polygon = exterior_polygon
                print("没有发现有效的内部孔洞，使用普通外部轮廓进行填充。")
            
            if not polygon.is_valid:
                polygon = polygon.buffer(0)
                if not polygon.is_valid or polygon.is_empty:
                    print("错误: 最终多边形（含孔洞）几何无效或为空。")
                    return None, None, None

            actual_offset = offset_distance if offset_distance is not None else row_spacing / 2.0
            actual_offset = -abs(actual_offset)
            offset_polygon = polygon.buffer(actual_offset, join_style=2)
            if not offset_polygon.is_valid or offset_polygon.is_empty:
                print(f"警告: 轮廓内缩 {actual_offset:.2f} 后无效或为空。")
                return None, None, None
        except Exception as e:
            print(f"错误: 创建Shapely Polygon或内缩时出错: {e}")
            return None, None, None

        step_axis = self.axis_index
        scan_axis = 1 - step_axis

        minx, miny, maxx, maxy = polygon.bounds
        min_step_val = miny if step_axis == 1 else minx
        max_step_val = maxy if step_axis == 1 else maxx
        scan_start_val = minx if scan_axis == 0 else miny
        scan_end_val = maxx if scan_axis == 0 else maxy
        scan_extension = row_spacing * 0.1

        all_scan_segments = []
        current_step_val = min_step_val + (row_spacing / 2.0) + 0.001
        
        # 预计算扫描线数量以优化内存分配
        num_scan_lines = int((max_step_val - current_step_val) / row_spacing) + 1
        
        while current_step_val < max_step_val:
            if step_axis == 1:
                scan_line = LineString([(scan_start_val - scan_extension, current_step_val), 
                                        (scan_end_val + scan_extension, current_step_val)])
            else:
                scan_line = LineString([(current_step_val, scan_start_val - scan_extension), 
                                        (current_step_val, scan_end_val + scan_extension)])
            try:
                intersection = offset_polygon.intersection(scan_line)
                if not intersection.is_empty:
                    if intersection.geom_type == 'LineString':
                        if intersection.length > 1e-6: 
                            all_scan_segments.append((intersection, current_step_val))
                    elif intersection.geom_type == 'MultiLineString':
                        for line_part in intersection.geoms:
                            if line_part.length > 1e-6: 
                                all_scan_segments.append((line_part, current_step_val))
            except Exception as e:
                print(f"警告: 步进 {current_step_val:.2f} 计算交集时出错: {e}")
            current_step_val += row_spacing

        if not all_scan_segments:
            print("警告: 未能在轮廓内生成任何有效填充线段。")
            return np.array([]), [], offset_polygon

        all_scan_segments.sort(key=lambda item: item[1])
        
        # 优化：预分配列表容量
        estimated_points = len(all_scan_segments) * 10  # 估算点数
        fill_points_list = []
        fill_points_list_extend = fill_points_list.extend  # 局部引用优化
        connection_types = []
        connection_types_extend = connection_types.extend  # 局部引用优化
        
        direction_forward = True
        total_pts_segments_before_interp, total_pts_segments_after_interp = 0, 0

        for seg_idx, (segment_geom, step_coord) in enumerate(all_scan_segments):
            coords_shapely = list(segment_geom.coords)
            seg_orig_pt_count = len(coords_shapely)
            total_pts_segments_before_interp += seg_orig_pt_count

            if (direction_forward and coords_shapely[0][scan_axis] > coords_shapely[-1][scan_axis]) or \
               (not direction_forward and coords_shapely[0][scan_axis] < coords_shapely[-1][scan_axis]):
                coords_shapely.reverse()
            
            current_segment_pts_np = np.array(coords_shapely)
            
            if max_segment_length is not None and max_segment_length > 0 and len(current_segment_pts_np) > 1:
                interpolated_pts_np = self.interpolate_path_points(current_segment_pts_np, max_segment_length)
                points_for_current_path_segment = interpolated_pts_np.tolist()
                types_for_current_path_segment = [0] * len(points_for_current_path_segment)
            else:
                points_for_current_path_segment = current_segment_pts_np.tolist()
                types_for_current_path_segment = [0] * len(points_for_current_path_segment)
            total_pts_segments_after_interp += len(points_for_current_path_segment)

            if not points_for_current_path_segment: 
                continue

            if fill_points_list:
                last_pt_in_main_list = np.array(fill_points_list[-1])
                first_pt_current_segment = np.array(points_for_current_path_segment[0])
                
                fill_points_list.append(points_for_current_path_segment[0])
                distance_to_new_segment_start = np.linalg.norm(first_pt_current_segment - last_pt_in_main_list)
                is_virtual_connection_to_start = distance_to_new_segment_start > (1.5 * row_spacing)
                connection_types.append(1 if is_virtual_connection_to_start else 0)

                fill_points_list_extend(points_for_current_path_segment[1:])
                connection_types_extend([0] * (len(points_for_current_path_segment) - 1))
            else:
                fill_points_list_extend(points_for_current_path_segment)
                connection_types_extend(types_for_current_path_segment)
            
            direction_forward = not direction_forward

        if not fill_points_list:
            print("警告: 构建Z字形路径后列表为空。")
            return np.array([]), [], offset_polygon

        final_fill_points_2d = np.array(fill_points_list)
        print(f"最终2D光栅填充含 {len(final_fill_points_2d)} 点 (含连接点), 虚拟连接点 {connection_types.count(1)} 个。")
        return final_fill_points_2d, connection_types, offset_polygon

    def _calculate_adjusted_spacing_parameters(self, contour_2d_points, target_3d_row_spacing, target_3d_offset_from_boundary, num_samples=10):
        """
        计算调整后的2D填充参数，以尝试在3D曲面上实现更均匀的间距。
        参数:
        contour_2d_points: (N, 2) numpy数组，XOY平面上的闭合轮廓顶点。
        target_3d_row_spacing: 期望在3D曲面上的行间距。
        target_3d_offset_from_boundary: 期望在3D曲面上的边界偏移。
        num_samples: 用于估算调整因子的采样点/线数量。
        返回:
        (adjusted_2d_row_spacing, adjusted_2d_offset_from_boundary): 调整后的2D行距和2D边界偏移。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or target_3d_row_spacing <= 0:
            return target_3d_row_spacing, target_3d_offset_from_boundary # 返回原始值如果输入无效

        try:
            polygon_2d = Polygon(contour_2d_points)
            if not polygon_2d.is_valid:
                polygon_2d = polygon_2d.buffer(0) # 尝试修复
            if not polygon_2d.is_valid or polygon_2d.is_empty:
                return target_3d_row_spacing, target_3d_offset_from_boundary
        except Exception:
            return target_3d_row_spacing, target_3d_offset_from_boundary

        min_x, min_y, max_x, max_y = polygon_2d.bounds
        step_axis = self.axis_index # 步进轴 (0=X, 1=Y)
        scan_axis = 1 - self.axis_index # 扫描轴
        
        # 在步进轴上选择采样点位置的比例
        current_sampling_proportions = [0.2, 0.5, 0.7, 0.9] 
        all_step_coords_for_sampling = [] # 存储步进轴上的采样坐标

        if step_axis == 0: # X轴步进
            for prop in current_sampling_proportions:
                all_step_coords_for_sampling.append(min_x + (max_x - min_x) * prop)
            step_min_extent, step_max_extent = min_x, max_x # 步进轴范围
            scan_start_coord, scan_end_coord = min_y, max_y # 扫描轴范围
        else: # Y轴步进
            for prop in current_sampling_proportions:
                all_step_coords_for_sampling.append(min_y + (max_y - min_y) * prop)
            step_min_extent, step_max_extent = min_y, max_y
            scan_start_coord, scan_end_coord = min_x, max_x
        
        # 如果轮廓在步进轴上的范围足够大，则在两端附近也添加采样点
        if (step_max_extent - step_min_extent) > 2 * target_3d_row_spacing:
            point_near_min_extent = step_min_extent + target_3d_row_spacing 
            point_near_max_extent = step_max_extent - target_3d_row_spacing
            if point_near_min_extent < (all_step_coords_for_sampling[0] if all_step_coords_for_sampling else step_max_extent):
                 all_step_coords_for_sampling.append(point_near_min_extent)
            if point_near_max_extent > (all_step_coords_for_sampling[-1] if all_step_coords_for_sampling else step_min_extent): 
                 all_step_coords_for_sampling.append(point_near_max_extent)

        all_step_coords_for_sampling = sorted(list(set(all_step_coords_for_sampling))) # 去重并排序
        
        all_measured_3d_distances_across_regions = [] # 存储所有测量的3D距离
        region_data_for_weighting = [] # 存储每个区域的加权数据
        
        if not all_step_coords_for_sampling:
            return target_3d_row_spacing, target_3d_offset_from_boundary

        for region_idx, center_step_coord_local in enumerate(all_step_coords_for_sampling):
            # 定义两条平行的采样线，它们在步进轴上的坐标相差 target_3d_row_spacing
            p1_step = center_step_coord_local - target_3d_row_spacing / 2
            p2_step = center_step_coord_local + target_3d_row_spacing / 2

            if step_axis == 0: # X轴步进, Y轴扫描
                current_sample_points_2d_line1 = np.array([[p1_step, y] for y in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
                current_sample_points_2d_line2 = np.array([[p2_step, y] for y in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
            else: # Y轴步进, X轴扫描
                current_sample_points_2d_line1 = np.array([[x, p1_step] for x in np.linspace(scan_start_coord, scan_end_coord, num_samples)])
                current_sample_points_2d_line2 = np.array([[x, p2_step] for x in np.linspace(scan_start_coord, scan_end_coord, num_samples)])

            # 筛选出两条线上都在原始2D轮廓内的点对
            valid_indices_line1 = [idx for idx, p_val in enumerate(current_sample_points_2d_line1) if polygon_2d.contains(Point(p_val))]
            valid_indices_line2 = [idx for idx, p_val in enumerate(current_sample_points_2d_line2) if polygon_2d.contains(Point(p_val))]
            common_valid_indices = sorted(list(set(valid_indices_line1) & set(valid_indices_line2))) # 两条线上都有效的点索引

            if not common_valid_indices or len(common_valid_indices) < 2: # 需要至少两个点对来估算
                continue

            filtered_points_2d_line1_local = current_sample_points_2d_line1[common_valid_indices]
            filtered_points_2d_line2_local = current_sample_points_2d_line2[common_valid_indices]

            # 将筛选后的2D点投影到3D表面
            projected_3d_line1_local, _, success_mask1_local = self.project_2d_points_to_3d_surface(filtered_points_2d_line1_local)
            projected_3d_line2_local, _, success_mask2_local = self.project_2d_points_to_3d_surface(filtered_points_2d_line2_local)

            measured_3d_distances_local = [] # 存储这个区域内测量的3D距离
            num_successful_pairs_in_region = 0
            for k_idx in range(len(projected_3d_line1_local)): # 遍历成功投影的点对
                if success_mask1_local[k_idx] and success_mask2_local[k_idx]:
                    p3d_1 = projected_3d_line1_local[k_idx]
                    p3d_2 = projected_3d_line2_local[k_idx]
                    dist_3d = np.linalg.norm(p3d_1 - p3d_2) # 计算3D距离
                    measured_3d_distances_local.append(dist_3d)
                    num_successful_pairs_in_region +=1
            
            if measured_3d_distances_local:
                avg_dist_local = np.mean(measured_3d_distances_local) # 这个区域的平均3D距离
                all_measured_3d_distances_across_regions.extend(measured_3d_distances_local) 
                region_data_for_weighting.append({
                    'avg_dist': avg_dist_local, 
                    'num_pairs': num_successful_pairs_in_region, 
                    'step_coord': center_step_coord_local
                })

        if not region_data_for_weighting: # 如果没有收集到任何有效的区域数据
            return target_3d_row_spacing, target_3d_offset_from_boundary

        sum_weighted_avg_dist = 0.0
        sum_weights = 0.0
        epsilon = 1e-6 # 用于比较浮点数的小值
        
        processed_regional_weights = []
        for r_data in region_data_for_weighting:
            avg_dist_r = r_data['avg_dist']
            deviation = abs(avg_dist_r - target_3d_row_spacing) # 与目标3D行距的偏差
            weight = deviation # 使用偏差作为权重 (偏差越大，权重越大，意味着这个区域的平均距离对最终加权平均的影响更大)
            processed_regional_weights.append({'step_coord': r_data['step_coord'], 'avg_dist': avg_dist_r, 'deviation': deviation, 'weight': weight, 'num_pairs':r_data['num_pairs']})
            sum_weighted_avg_dist += avg_dist_r * weight
            sum_weights += weight

        avg_measured_3d_spacing = target_3d_row_spacing # 默认值
        if sum_weights < epsilon: # 如果总权重过小 (例如，所有区域的平均距离都非常接近目标距离)
            if all_measured_3d_distances_across_regions:
                avg_measured_3d_spacing = np.mean(all_measured_3d_distances_across_regions) # 使用简单平均值
        else:
            weighted_avg_measured_3d_spacing = sum_weighted_avg_dist / sum_weights # 加权平均的3D间距
            avg_measured_3d_spacing = weighted_avg_measured_3d_spacing 
        
        if avg_measured_3d_spacing < 1e-6: # 避免除以零或过小的值
            return target_3d_row_spacing, target_3d_offset_from_boundary

        # 计算调整因子
        adjustment_factor = target_3d_row_spacing / avg_measured_3d_spacing
        adjustment_factor = np.clip(adjustment_factor, 0.5, 2.0) # 限制调整因子的范围，防止过度调整
        
        adjusted_2d_row_spacing = target_3d_row_spacing * adjustment_factor
        adjusted_2d_offset_from_boundary = target_3d_offset_from_boundary * adjustment_factor

        print(f"  参数调整(投影策略): 目标3D行距 {target_3d_row_spacing:.3f}, 实测平均3D间距 {avg_measured_3d_spacing:.3f}, 调整因子 {adjustment_factor:.3f}")
        print(f"  调整后2D行距: {adjusted_2d_row_spacing:.3f}, 调整后2D偏移: {adjusted_2d_offset_from_boundary:.3f}")

        return adjusted_2d_row_spacing, adjusted_2d_offset_from_boundary

    def generate_2d_concentric_fill(self, contour_2d_points, row_spacing, offset_distance=None, max_segment_length=None, inner_contours_list=None):
        """
        在给定的二维轮廓内生成同心圆填充路径，同时考虑内部孔洞。
        参数与 generate_2d_raster_fill 类似。
        inner_contours_list: 可选，内部轮廓（孔洞）的列表，每个元素为(N, 2) numpy数组。
        返回:
        (fill_points_2d, connection_types, initial_offset_polygon) 或 (None, None, None) 如果失败。
        fill_points_2d: (M, 2) numpy数组，生成的2D填充点。
        connection_types: (M,) list，标记每个点如何连接到前一个点 (0=填充, 1=空程)。
        initial_offset_polygon: 初始内缩后的Shapely多边形对象。
        """
        if contour_2d_points is None or len(contour_2d_points) < 3 or row_spacing <= 0:
            print("错误: 轮廓无效或行距不正确，无法生成2D同心圆填充。")
            return None, None, None

        try:
            # 创建外部轮廓多边形
            exterior_polygon = Polygon(contour_2d_points)
            if not exterior_polygon.is_valid:
                exterior_polygon = exterior_polygon.buffer(0) # 尝试修复
            if not exterior_polygon.is_valid or exterior_polygon.is_empty:
                print("错误: 外部轮廓几何无效或为空，无法进行同心圆填充。")
                return None, None, None
                
            # 处理内部轮廓（孔洞）
            holes = []
            if inner_contours_list:
                for inner_contour in inner_contours_list:
                    if inner_contour is not None and len(inner_contour) >= 3:
                        try:
                            inner_poly = Polygon(inner_contour)
                            if inner_poly.is_valid and not inner_poly.is_empty and exterior_polygon.contains(inner_poly):
                                holes.append(inner_contour)
                            elif not inner_poly.is_valid:
                                fixed_inner = inner_poly.buffer(0)
                                if fixed_inner.is_valid and not fixed_inner.is_empty and exterior_polygon.contains(fixed_inner):
                                    holes.append(inner_contour)
                        except Exception as inner_e:
                            print(f"警告: 处理内部轮廓时出错 (同心圆): {inner_e}")
            
            # 创建包含孔洞的多边形
            if holes:
                original_polygon = Polygon(contour_2d_points, holes=holes)
                print(f"同心圆填充: 创建了包含 {len(holes)} 个孔洞的多边形用于填充。")
            else:
                original_polygon = exterior_polygon
                print("同心圆填充: 没有发现有效的内部孔洞，使用普通外部轮廓进行填充。")
                
            if not original_polygon.is_valid:
                original_polygon = original_polygon.buffer(0) # 尝试再次修复
            if not original_polygon.is_valid or original_polygon.is_empty:
                print("错误: 最终多边形（含孔洞）几何无效或为空，无法进行同心圆填充。")
                return None, None, None

            actual_offset_for_fill_area = offset_distance if offset_distance is not None else row_spacing / 2.0
            actual_offset_for_fill_area = -abs(actual_offset_for_fill_area) # 确保向内偏移
            
            current_fill_polygon = original_polygon.buffer(actual_offset_for_fill_area, join_style=2) # join_style=2 (MITRE)
            if not current_fill_polygon.is_valid or current_fill_polygon.is_empty:
                print(f"警告: 轮廓内缩 {actual_offset_for_fill_area:.2f} (用于填充区域) 后无效或为空。无法生成同心圆填充。")
                return None, None, original_polygon.buffer(0) # 返回原始多边形（修复后）
            
            initial_offset_polygon_for_return = current_fill_polygon.buffer(0) # 保存初始内缩后的多边形

        except Exception as e:
            print(f"错误: 创建Shapely Polygon或初始内缩时出错 (同心圆): {e}")
            return None, None, None

        all_concentric_paths_points = [] # 存储所有同心圆路径点
        all_connection_types = [] # 0表示填充, 1表示到新轮廓起点的空程
        min_area_threshold = (row_spacing**2) / 8 # 启发式阈值，用于提前停止处理非常小的区域
        loop_count = 0
        max_loops = 200 # 防止无限循环

        while current_fill_polygon.is_valid and not current_fill_polygon.is_empty and current_fill_polygon.area > min_area_threshold and loop_count < max_loops:
            loop_count += 1
            
            polygons_to_process_this_iteration = [] # 当前迭代要处理的多边形
            if current_fill_polygon.geom_type == 'Polygon':
                polygons_to_process_this_iteration = [current_fill_polygon]
            elif current_fill_polygon.geom_type == 'MultiPolygon':
                # 如果是多多边形，优先处理面积最大的，也可以扩展为处理所有多边形
                polygons_to_process_this_iteration = sorted(list(current_fill_polygon.geoms), key=lambda p: p.area, reverse=True)
            else: # 其他类型 (例如，如果完全收缩成线串)
                break 

            next_outer_polygons_for_next_iteration = [] # 用于下一个同心壳的多边形
            
            for poly_idx, current_poly_shell in enumerate(polygons_to_process_this_iteration):
                if not current_poly_shell.is_valid or current_poly_shell.is_empty or current_poly_shell.area < min_area_threshold:
                    continue

                contour_coords_np = np.array(current_poly_shell.exterior.coords) # 获取外轮廓坐标
                
                # Shapely的exterior.coords通常是(N+1,2)且首尾点相同
                # 为了插值函数，如果首尾点相同，则移除末尾的重复点
                if np.allclose(contour_coords_np[0], contour_coords_np[-1]):
                    contour_coords_np = contour_coords_np[:-1]

                if len(contour_coords_np) < self.min_points_req : # 插值前至少需要最少点数
                    continue

                # 对轮廓进行插值
                interpolated_contour_np = self.interpolate_path_points(contour_coords_np, max_segment_length)
                
                # 重新闭合路径段数据 (如果interpolate_path_points不闭合它)
                if not np.allclose(interpolated_contour_np[0], interpolated_contour_np[-1]):
                     interpolated_contour_np = np.vstack([interpolated_contour_np, interpolated_contour_np[0]])
                
                if len(interpolated_contour_np) < self.min_points_req: # 插值后再次检查点数
                    continue 
                
                # 添加到路径列表
                if not all_concentric_paths_points: # 如果是第一个路径段
                    all_concentric_paths_points.extend(interpolated_contour_np.tolist())
                    all_connection_types.extend([0] * len(interpolated_contour_np)) # 全是填充
                else:
                    # 添加到新轮廓起点的空程
                    all_concentric_paths_points.append(interpolated_contour_np[0].tolist()) # 新轮廓的第一个点
                    all_connection_types.append(1) # 标记为空程连接
                    # 添加轮廓的其余点
                    all_concentric_paths_points.extend(interpolated_contour_np[1:].tolist())
                    all_connection_types.extend([0] * (len(interpolated_contour_np) - 1)) # 这些是填充

                # 从current_poly_shell生成下一个内层多边形
                inner_poly_candidate = current_poly_shell.buffer(-row_spacing, join_style=2) # join_style=2 (MITRE)
                if inner_poly_candidate.is_valid and not inner_poly_candidate.is_empty and inner_poly_candidate.area > min_area_threshold:
                    if inner_poly_candidate.geom_type == 'Polygon':
                        next_outer_polygons_for_next_iteration.append(inner_poly_candidate)
                    elif inner_poly_candidate.geom_type == 'MultiPolygon':
                        next_outer_polygons_for_next_iteration.extend(list(inner_poly_candidate.geoms))
            
            if not next_outer_polygons_for_next_iteration: # 如果没有有效的内层多边形候选
                break
            
            # 从收集到的有效内层候选多边形重建current_fill_polygon
            # 如果有多个，取面积最大的或将它们合并。为简单起见，取面积最大的。
            valid_next_polys = [p for p in next_outer_polygons_for_next_iteration if p.is_valid and not p.is_empty and p.area > min_area_threshold]
            if not valid_next_polys:
                    break
            current_fill_polygon = max(valid_next_polys, key=lambda p: p.area) # 简化处理：取面积最大的
        
        if not all_concentric_paths_points:
            print("警告: 未能生成任何有效的2D同心圆填充路径点。")
            return np.array([]), [], initial_offset_polygon_for_return # 返回空数组和初始内缩多边形

        final_fill_points_2d = np.array(all_concentric_paths_points)
        print(f"2D同心圆填充路径构建完成，包含 {len(final_fill_points_2d)} 点，{all_connection_types.count(1)} 个环间连接。")
        return final_fill_points_2d, all_connection_types, initial_offset_polygon_for_return

    def project_2d_points_to_3d_surface(self, points_2d):
        """
        将一组2D点（XOY平面）投影到三维网格表面。
        优化版本：使用批量射线投影提高性能。
        参数:
        points_2d: (N, 2) numpy数组，包含要投影的2D点。
        返回:
        (projected_points_3d, corresponding_normals_3d, success_mask)
        projected_points_3d: (M, 3) numpy数组，成功投影的三维点。
        corresponding_normals_3d: (M, 3) numpy数组，对应的表面法线。
        success_mask: (N,) boolean数组，标记哪些原始2D点成功投影。
        """
        if points_2d is None or len(points_2d) == 0:
            return np.array([]), np.array([]), np.array([])

        num_points = len(points_2d)
        # 设置射线起点在网格Z轴范围之上，方向朝下
        z_min, z_max = self.mesh_bounds[0, 2], self.mesh_bounds[1, 2]
        ray_origin_z = z_max + abs(z_max - z_min) + 1.0
        
        # 批量构建射线起点和方向
        ray_origins = np.column_stack([points_2d, np.full(num_points, ray_origin_z)])
        ray_directions = np.tile([0, 0, -1], (num_points, 1))
        
        # 批量射线投影
        locations, index_ray, index_tri = self.mesh.ray.intersects_location(
            ray_origins=ray_origins, 
            ray_directions=ray_directions
        )
        
        # 初始化结果
        projected_points_3d_list = []
        normals_3d_list = []
        success_mask = np.zeros(num_points, dtype=bool)
        
        if len(locations) > 0:
            # 批量处理命中结果
            # 为每个射线索引找到第一个（最近的）交点
            unique_ray_indices = np.unique(index_ray)
            for ray_idx in unique_ray_indices:
                mask = index_ray == ray_idx
                if np.any(mask):
                    # 取该射线的第一个交点（通常是最近的）
                    first_hit_idx = np.where(mask)[0][0]
                    hit_point = locations[first_hit_idx]
                    
                    projected_points_3d_list.append(hit_point)
                    # 使用预计算的法线而不是每次重新计算
                    normal = self.get_surface_normal_at_point(hit_point)
                    normals_3d_list.append(normal)
                    success_mask[ray_idx] = True

        if not projected_points_3d_list:
            return np.array([]), np.array([]), success_mask
            
        return np.array(projected_points_3d_list), np.array(normals_3d_list), success_mask

    def build_final_3d_paths(self, projected_points_3d, projected_normals_3d, success_mask_2d, 
                               original_2d_points, original_connection_types, 
                               boundary_paths_data=None, projected_segment_id_start=-1):
        """
        根据投影后的3D点和原始2D连接类型构建最终的分段3D路径。
        参数:
        projected_points_3d: 成功投影的3D点数组。
        projected_normals_3d: 对应的3D法线数组。
        success_mask_2d: 标记哪些原始2D点成功投影。
        original_2d_points: 原始的2D填充点数组。
        original_connection_types: 原始2D点的连接类型 (0=填充, 1=空程)。
        boundary_paths_data: 可选，从get_projected_boundary_contours获取的边界数据列表。
        projected_segment_id_start: 投影填充段的起始ID。
        返回:
        final_paths_list: 包含边界和投影填充段的路径数据列表。每个元素是 (points_3d, normals_3d, is_boundary, segment_id)。
        """
        final_paths_list = []
        current_segment_id = projected_segment_id_start # 当前填充段的ID

        # 如果提供了边界路径数据，首先添加它们
        if boundary_paths_data:
            for b_data in boundary_paths_data:
                b_points_3d = b_data['points_3d']
                # 使用批量法线计算优化性能
                b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                final_paths_list.append((b_points_3d, b_normals_3d, True, b_data['id']))

        valid_original_2d_indices = np.where(success_mask_2d)[0] # 获取成功投影的原始2D点的索引
        if len(valid_original_2d_indices) == 0 or len(projected_points_3d) == 0:
            print("没有足够的成功投影点来构建3D填充路径。")
            return final_paths_list # 可能只包含边界路径

        current_3d_path_points = [] # 当前正在构建的3D路径段的点
        current_3d_path_normals = [] # 当前路径段的法线
        projected_idx_counter = 0 # 对应于projected_points_3d和projected_normals_3d的计数器

        for i in range(len(original_2d_points)): # 遍历所有原始2D点
            if not success_mask_2d[i]: # 如果原始2D点未成功投影
                # 如果这个未成功投影的点本应是一个空程的起点，并且当前有正在构建的路径段，则结束当前段
                if original_connection_types[i] == 1 and current_3d_path_points: 
                    if len(current_3d_path_points) >= self.min_points_req:
                        final_paths_list.append((np.array(current_3d_path_points), 
                                                 np.array(current_3d_path_normals), 
                                                 False, current_segment_id))
                        current_segment_id -= 1 # 更新下一个填充段的ID
                    current_3d_path_points = [] # 重置当前路径段
                    current_3d_path_normals = []
                continue # 跳过未成功投影的点

            # 获取对应的3D点和法线
            p3d = projected_points_3d[projected_idx_counter]
            n3d = projected_normals_3d[projected_idx_counter]
            # original_connection_types[i] 指的是 original_2d_points[i] 这个点
            # 如果 original_connection_types[i] == 1, 表示这个点是新段的起点 (通过空程到达)
            is_start_of_travel_or_new_segment = (original_connection_types[i] == 1)
            projected_idx_counter += 1 

            if is_start_of_travel_or_new_segment: # 这个点是通过"空程"移动到达的
                if current_3d_path_points: # 结束之前的实际打印段
                    if len(current_3d_path_points) >= self.min_points_req:
                        final_paths_list.append((np.array(current_3d_path_points), 
                                                 np.array(current_3d_path_normals), 
                                                 False, current_segment_id))
                        current_segment_id -= 1
                    current_3d_path_points = [] # 重置
                    current_3d_path_normals = []
                
                # 用这个点开始一个新的段 (它是新填充部分的第一个点)
                current_3d_path_points.append(p3d)
                current_3d_path_normals.append(n3d)
        
            else: # 这个点是正在进行的打印段的一部分
                if not current_3d_path_points: # 如果是整体的第一个点，开始新段
                    current_3d_path_points.append(p3d)
                    current_3d_path_normals.append(n3d)
                else: # 添加到当前段
                    current_3d_path_points.append(p3d)
                    current_3d_path_normals.append(n3d)
        
        # 添加最后一个路径段 (如果有)
        if current_3d_path_points and len(current_3d_path_points) >= self.min_points_req:
            final_paths_list.append((np.array(current_3d_path_points), 
                                     np.array(current_3d_path_normals), 
                                     False, current_segment_id))
        
        num_fill_segments_added = sum(1 for _, _, is_b, _ in final_paths_list if not is_b and (_ if boundary_paths_data is None else True))
        if boundary_paths_data: # 如果是从build_final_3d_paths内部调用（只处理填充）
             num_fill_segments_added = len(final_paths_list)


        print(f"构建了 {num_fill_segments_added} 个3D投影填充段。")
        return final_paths_list

    def compute_boundary_distances_batch(self, points_3d, boundary_linestrings):
        """
        批量计算点到边界的最小距离
        
        参数:
        points_3d: (N, 3) numpy数组，要计算距离的3D点
        boundary_linestrings: Shapely LineString对象列表
        
        返回:
        distances: (N,) numpy数组，每个点到最近边界的距离
        """
        if not boundary_linestrings:
            return np.full(len(points_3d), float('inf'))
        
        distances = np.full(len(points_3d), float('inf'))
        
        for i, point_3d in enumerate(points_3d):
            point_2d = Point(point_3d[:2])  # 使用XY坐标创建2D点
            min_distance = float('inf')
            
            for boundary_line in boundary_linestrings:
                try:
                    dist = boundary_line.distance(point_2d)
                    min_distance = min(min_distance, dist)
                except Exception:
                    continue  # 跳过计算失败的情况
            
            distances[i] = min_distance
        
        return distances

    def _calculate_actual_3d_spacing_between_strip_sets(self, strips_layer1_dicts, strips_layer2_dicts, scan_axis, num_samples_on_strip1=10):
        """
        计算两组3D路径条带之间的实际间距统计
        ENHANCED: 添加智能缓存、性能优化和质量改进
        """
        # 更新性能统计
        self._performance_stats['total_spacing_calculations'] += 1

        if not strips_layer1_dicts or not strips_layer2_dicts:
            return self._create_empty_spacing_result()

        # 生成缓存键
        cache_key = self._generate_spacing_cache_key(strips_layer1_dicts, strips_layer2_dicts, num_samples_on_strip1)

        # 检查缓存
        cached_result = self._get_cached_spacing_result(cache_key)
        if cached_result is not None:
            return cached_result

        total_strip1_length = 0
        valid_strips1 = []
        valid_strips2_points_list = [] # Collect all points from valid_strips2 here

        # 注意：Python列表会自动调整大小，无需手动预分配容量

        for strip1_data in strips_layer1_dicts:
            strip1_points = strip1_data.get('points') if isinstance(strip1_data, dict) else strip1_data
            if not isinstance(strip1_points, np.ndarray) or strip1_points is None or len(strip1_points) < 2:
                continue
            valid_strips1.append(strip1_points)
            total_strip1_length += self._calculate_polyline_length_fast(strip1_points)

        for strip2_data in strips_layer2_dicts:
            strip2_points = strip2_data.get('points') if isinstance(strip2_data, dict) else strip2_data
            if not isinstance(strip2_points, np.ndarray) or strip2_points is None or len(strip2_points) < 1: # Need at least 1 point for KDTree
                continue
            # Ensure points are 3D for vstack compatibility later, even if it's a single point
            if strip2_points.ndim == 1 and strip2_points.shape[0] == 3:
                 valid_strips2_points_list.append(strip2_points.reshape(1,3))
            elif strip2_points.ndim == 2 and strip2_points.shape[1] == 3:
                 valid_strips2_points_list.append(strip2_points)

        if not valid_strips1 or not valid_strips2_points_list:
            result = self._create_empty_spacing_result()
            self._cache_spacing_result(cache_key, result)
            return result

        # 增强: 预构建KDTree并添加空间索引缓存
        target_kdtree_layer2 = None
        kdtree_cache_key = None

        try:
            all_points_layer2_array = np.vstack(valid_strips2_points_list)
            if all_points_layer2_array.shape[0] > 0:
                # 生成KDTree缓存键
                kdtree_cache_key = hash(all_points_layer2_array.tobytes())

                # 检查KDTree缓存
                if kdtree_cache_key in self._spatial_index_cache:
                    target_kdtree_layer2 = self._spatial_index_cache[kdtree_cache_key]
                else:
                    target_kdtree_layer2 = cKDTree(all_points_layer2_array)
                    # 缓存KDTree（限制缓存大小）
                    if len(self._spatial_index_cache) >= 20:
                        # 移除最旧的缓存项
                        oldest_key = next(iter(self._spatial_index_cache))
                        del self._spatial_index_cache[oldest_key]
                    self._spatial_index_cache[kdtree_cache_key] = target_kdtree_layer2
        except (ValueError, Exception):
            target_kdtree_layer2 = None

        if target_kdtree_layer2 is None:
            result = self._create_empty_spacing_result()
            self._cache_spacing_result(cache_key, result)
            return result

        # 增强: 智能自适应采样策略
        avg_strip_length = total_strip1_length / len(valid_strips1)
        target_spacing_estimate = 0.4  # 估算的目标间距，可以从参数传入

        # 基于条带长度和复杂度的动态采样
        if avg_strip_length < 2.0:
            effective_samples = max(3, int(num_samples_on_strip1 * 0.9))
        elif avg_strip_length > 10.0:
            effective_samples = min(18, int(num_samples_on_strip1 * 1.3))
        else:
            effective_samples = int(num_samples_on_strip1)

        all_distances = []
        total_sample_points = 0

        for strip1_points in valid_strips1:
            strip_length = self._calculate_polyline_length_fast(strip1_points)
            current_samples = effective_samples

            # 基于条带长度的精细调整
            if strip_length < 1.0:
                current_samples = max(2, int(effective_samples * 0.8))
            elif strip_length > 8.0:
                current_samples = min(25, int(effective_samples * 1.4))

            # 使用增强的智能采样
            sample_indices = self._enhanced_smart_sampling(strip1_points, current_samples, target_spacing_estimate)
            total_sample_points += len(sample_indices)

            for idx in sample_indices:
                sample_point = strip1_points[idx]

                # 使用优化的距离计算
                min_distance = self._calculate_min_distance_to_strips_optimized(sample_point, target_kdtree_layer2)

                if min_distance is not None and 0.01 < min_distance < 20.0:  # 更严格的合理性检查
                    all_distances.append(min_distance)

        if not all_distances:
            result = self._create_empty_spacing_result()
            self._cache_spacing_result(cache_key, result)
            return result

        # 增强: 使用改进的异常值过滤
        filtered_distances = self._filter_outliers_improved(all_distances)

        if not filtered_distances:
            result = self._create_empty_spacing_result()
            self._cache_spacing_result(cache_key, result)
            return result

        distances_array = np.array(filtered_distances)

        # 计算增强的统计信息
        result = {
            'mean': float(np.mean(distances_array)),
            'min': float(np.min(distances_array)),
            'max': float(np.max(distances_array)),
            'median': float(np.median(distances_array)),
            'std': float(np.std(distances_array)),
            'samples': len(filtered_distances),
            'raw_distances': filtered_distances,
            'total_sample_points': total_sample_points,  # 新增: 总采样点数
            'filter_efficiency': len(filtered_distances) / len(all_distances) if all_distances else 0.0  # 新增: 过滤效率
        }

        # 缓存结果
        self._cache_spacing_result(cache_key, result)

        return result



    def _point_to_polyline_distance(self, point, polyline):
        """
        Calculate the minimum distance from a 3D point to a 3D polyline.
        This is the true geometric distance used for parallel polyline spacing.
        
        Args:
            point (numpy.ndarray): 3D point [x, y, z]
            polyline (numpy.ndarray): Array of 3D points forming the polyline
            
        Returns:
            float: Minimum distance from point to polyline
        """
        if len(polyline) < 2:
            return float('inf')
        
        min_distance = float('inf')
        
        # 检查到每个线段的距离
        for i in range(len(polyline) - 1):
            seg_start = polyline[i]
            seg_end = polyline[i + 1]
            
            # 计算点到线段的距离
            distance = self._point_to_line_segment_distance(point, seg_start, seg_end)
            min_distance = min(min_distance, distance)
        
        return min_distance

    def _point_to_line_segment_distance(self, point, seg_start, seg_end):
        """
        Calculate the minimum distance from a 3D point to a 3D line segment.
        Uses the standard geometric formula for point-to-line distance.
        
        Args:
            point (numpy.ndarray): 3D point [x, y, z]
            seg_start (numpy.ndarray): Line segment start point [x, y, z]
            seg_end (numpy.ndarray): Line segment end point [x, y, z]
            
        Returns:
            float: Distance from point to line segment
        """
        # 向量计算
        v = seg_end - seg_start  # 线段向量
        w = point - seg_start    # 从线段起点到测试点的向量
        
        # 线段长度的平方
        v_length_sq = np.dot(v, v)
        
        if v_length_sq < 1e-12:  # 退化线段（两端点相同）
            return np.linalg.norm(w)
        
        # 计算投影参数 t
        t = np.dot(w, v) / v_length_sq
        
        # 限制 t 在 [0, 1] 范围内（确保投影点在线段上）
        t = max(0.0, min(1.0, t))
        
        # 计算线段上最近点
        closest_point = seg_start + t * v
        
        # 返回距离
        return np.linalg.norm(point - closest_point)

    def _calculate_polyline_length(self, polyline):
        """
        Calculate the total length of a 3D polyline.
        
        Args:
            polyline (numpy.ndarray): Array of 3D points
            
        Returns:
            float: Total length of the polyline
        """
        if len(polyline) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(len(polyline) - 1):
            segment_length = np.linalg.norm(polyline[i + 1] - polyline[i])
            total_length += segment_length
        
        return total_length

    def _filter_outliers_advanced(self, distances):
        """
        Advanced outlier filtering for distance measurements.
        Uses a combination of IQR and domain knowledge filtering.
        
        Args:
            distances (numpy.ndarray): Array of distance measurements
            
        Returns:
            numpy.ndarray: Filtered distances
        """
        if len(distances) < 3:
            return distances
        
        # 第一步：移除明显不合理的值
        # 间距不应该超过目标间距的10倍或小于目标间距的0.1倍
        target_spacing = 0.32  # mm，这应该从参数传入，这里先硬编码
        reasonable_min = target_spacing * 0.05  # 最小不小于目标的5%
        reasonable_max = target_spacing * 20     # 最大不超过目标的20倍
        
        mask1 = (distances >= reasonable_min) & (distances <= reasonable_max)
        filtered1 = distances[mask1]
        
        if len(filtered1) < 3:
            return filtered1
        
        # 第二步：IQR异常值过滤
        q25 = np.percentile(filtered1, 25)
        q75 = np.percentile(filtered1, 75)
        iqr = q75 - q25
        
        if iqr > 1e-6:  # 避免除零
            # 动态调整IQR倍数：如果数据分散度大，使用更宽松的阈值
            iqr_multiplier = 2.0 if iqr > target_spacing * 0.5 else 1.5
            
            lower_bound = q25 - iqr_multiplier * iqr
            upper_bound = q75 + iqr_multiplier * iqr
            
            mask2 = (filtered1 >= lower_bound) & (filtered1 <= upper_bound)
            filtered2 = filtered1[mask2]
            
            # 确保至少保留原始数据的50%
            if len(filtered2) >= len(distances) * 0.5:
                return filtered2
        else:
                return filtered1
        
        return filtered1
        

    def _calculate_adaptive_sampling_count(self, points_array, base_num_samples, scan_axis):
        """
        根据条带的几何特性计算自适应采样数量
        """
        if len(points_array) <= 2:
            return len(points_array)
        
        # 计算条带的总长度
        segment_lengths = np.linalg.norm(np.diff(points_array, axis=0), axis=1)
        total_length = np.sum(segment_lengths)
        
        # 计算曲率变化 (简化版本)
        if len(points_array) >= 3:
            # 计算方向变化
            directions = np.diff(points_array, axis=0)
            direction_norms = np.linalg.norm(directions, axis=1)
            # 避免除零
            direction_norms = np.where(direction_norms > 1e-8, direction_norms, 1e-8)
            normalized_directions = directions / direction_norms[:, np.newaxis]
            
            if len(normalized_directions) >= 2:
                direction_changes = np.sum(np.abs(np.diff(normalized_directions, axis=0)), axis=1)
                avg_curvature = np.mean(direction_changes)
            else:
                avg_curvature = 0.0
        else:
            avg_curvature = 0.0
        
        # 根据长度和曲率调整采样数量
        length_factor = max(1.0, total_length / 2.0)  # 每2mm至少一个采样点
        curvature_factor = 1.0 + avg_curvature * 2.0  # 曲率越大采样越密
        
        adaptive_samples = int(base_num_samples * length_factor * curvature_factor)
        # 限制采样数量的范围
        adaptive_samples = max(base_num_samples, min(adaptive_samples, len(points_array), base_num_samples * 3))
        
        return adaptive_samples

    def _brute_force_nearest_search(self, query_point, target_points_array, max_results=5):
        """
        暴力搜索最近的几个点
        """
        if len(target_points_array) == 0:
            return []
        
        distances = np.linalg.norm(target_points_array - query_point, axis=1)
        sorted_indices = np.argsort(distances)
        
        # 返回最近的几个距离
        k = min(max_results, len(distances))
        return distances[sorted_indices[:k]].tolist()

    def _create_empty_spacing_result(self):
        """
        创建一个空的间距统计结果，用于失败情况。
        """
        return {
            'mean': float('nan'),
            'min': float('nan'),
            'max': float('nan'),
            'median': float('nan'),
            'std': float('nan'),
            'samples': 0,
            'raw_distances': []
            }

    def _create_direct_offset_paths(self, row_spacing, offset_from_bounds, max_segment_length, 
                                  proximity_threshold=0.15, adaptive_density=True, 
                                  iter_min_delta_y_factor=0.05,
                                  iter_max_delta_y_factor=2.0,
                                  iter_tolerance_abs=0.1,
                                  iter_max_iterations_per_step=15,
                                  iter_num_samples_for_spacing_calc=7
                                  ):
        """
        新策略：直接在3D曲面上通过沿一个轴偏置（切片）并连接路径条带来生成填充。
        如果启用自适应密度，则使用基于迭代反馈的方法 (`generate_adaptive_slice_positions_iterative`) 
        来确定切片位置，以尝试在3D表面上获得更均匀的路径间距。
        
        参数:
        row_spacing: 沿偏置轴的基础路径条带间距。
        offset_from_bounds: 从网格在偏置轴上的边界开始向内偏移的距离。
        max_segment_length: 路径条带上点的最大允许间距。
        proximity_threshold: (当前在此方法中不直接使用进行过滤) 与边界和孔洞的最小距离，主要用于影响传递给迭代切片器的孔洞多边形。
        adaptive_density: 是否启用自适应密度控制 (通过迭代方法)。
        iter_min_delta_y_factor, iter_max_delta_y_factor, iter_tolerance_abs, 
        iter_max_iterations_per_step, iter_num_samples_for_spacing_calc: 控制迭代自适应切片行为的参数。
        
        返回:
        final_paths_list: 包含边界和直接偏置填充段的路径数据列表。
        spacing_analysis_data: 包含间距分析数据的列表 (如果适用)。
        """
        print(f"\n--- 开始执行直接偏置填充策略 ---")
        print(f"  基础行距: {row_spacing:.3f} mm")
        print(f"  边界内缩: {offset_from_bounds:.3f} mm")
        print(f"  最大段长: {max_segment_length:.3f} mm")
        print(f"  自适应密度: {'启用' if adaptive_density else '禁用'}")
        if adaptive_density:
            # Log for iterative method (already updated in previous step if it was part of main changes)
            # This section is now simplified as old methods are removed.
            print(f"  直接偏置策略 - 使用迭代反馈自适应间距方法")
            print(f"    迭代参数: MinΔYFactor={iter_min_delta_y_factor:.2f}, MaxΔYFactor={iter_max_delta_y_factor:.2f}, AbsTol={iter_tolerance_abs:.2f}mm")

        final_paths_list = []
        fill_segment_id_counter = -10000 # 为填充段使用较大的负ID起始值
        
        # 获取投影的边界轮廓并区分内外部
        all_boundary_contour_info = self.get_projected_boundary_contours()
        external_contours_3d = []
        internal_contours_3d = []
        # shapely_boundary_linestrings = [] # 存储所有边界的Shapely LineString对象 # DEPRECATED by all_3d_boundary_linestrings
        shapely_internal_linestrings = [] # 存储内部轮廓的Shapely LineString对象（用于孔洞检测）
        
        # Helper function to convert 3D points to Shapely LineString
        # Moved here to be available before its first use.
        def to_shapely_linestring_3d(points):
            if len(points) < 2:
                return None
            return LineString(points)

        # Prepare 3D boundary polylines (Shapely LineStrings) for intersection logic
        all_3d_boundary_linestrings = []
        if all_boundary_contour_info:
            print(f"  直接偏置策略: 正在准备 {len(all_boundary_contour_info)} 个3D边界轮廓线...")
            for contour_data in all_boundary_contour_info:
                if contour_data['points_3d'] is not None and len(contour_data['points_3d']) >= 2:
                    boundary_poly_3d = to_shapely_linestring_3d(contour_data['points_3d'])
                    if boundary_poly_3d:
                        all_3d_boundary_linestrings.append(boundary_poly_3d)
                    else:
                        print(f"    警告: 未能为轮廓ID {contour_data['id']} 创建3D LineString (点数: {len(contour_data['points_3d'])})")
                else:
                    print(f"    警告: 轮廓ID {contour_data['id']} 没有足够的3D点 (点数: {len(contour_data['points_3d']) if contour_data['points_3d'] is not None else 'None'})")
        
        if not all_3d_boundary_linestrings:
            print("  警告: 直接偏置策略 - 未找到有效的3D边界轮廓线用于交点检测。填充段可能不准确或无法生成。")
        else:
            print(f"  直接偏置策略: 成功准备 {len(all_3d_boundary_linestrings)} 个3D边界轮廓线 (Shapely LineString)。")

        if all_boundary_contour_info: # This block remains for now for external/internal distinction, though its direct use might change
            # 区分内部和外部轮廓
            for contour_data in all_boundary_contour_info:
                contour_2d = contour_data['points_2d']
                contour_poly = None
                try:
                    contour_poly = Polygon(contour_2d)
                    if not contour_poly.is_valid:
                        contour_poly = contour_poly.buffer(0)  # 尝试修复
                except Exception as e:
                    print(f"警告: 处理轮廓 {contour_data['id']} 时出错: {e}")
                    continue
                
                # 检查此轮廓是否包含在其他轮廓内
                is_internal = False
                for other_data in all_boundary_contour_info:
                    if other_data['id'] == contour_data['id']:
                        continue  # 跳过自身比较
                    
                    other_poly = None
                    try:
                        other_poly = Polygon(other_data['points_2d'])
                        if not other_poly.is_valid:
                            other_poly = other_poly.buffer(0)
                        
                        if other_poly.contains(contour_poly):
                            is_internal = True
                            break
                    except Exception as e:
                        print(f"警告: 比较轮廓 {contour_data['id']} 和 {other_data['id']} 时出错: {e}")
                        continue
                
                if is_internal:
                    internal_contours_3d.append(contour_data)
                    # 为内部轮廓创建3D LineString用于穿越检测
                    if len(contour_data['points_3d']) >= 2:
                        shapely_internal_linestrings.append(LineString(contour_data['points_3d']))
                    print(f"直接偏置策略: 轮廓 {contour_data['id']} 识别为内部轮廓（孔洞）")
                else:
                    external_contours_3d.append(contour_data)
                    print(f"直接偏置策略: 轮廓 {contour_data['id']} 识别为外部轮廓")
                    
                # 为所有轮廓创建LineString（用于边界距离检查）
                if len(contour_data['points_3d']) >= 2:
                    # shapely_boundary_linestrings.append(LineString(contour_data['points_3d']))
                    pass
        
        # 1. 添加3D边界路径（包括内部和外部）
        boundary_id_counter = 1
        for contour_data in all_boundary_contour_info:
            b_points_3d = contour_data['points_3d']
            if len(b_points_3d) >= self.min_points_req:
                # 使用批量法线计算优化性能
                b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                final_paths_list.append((b_points_3d, b_normals_3d, True, contour_data['id']))
                boundary_id_counter += 1
            else:
                print(f"直接偏置策略：跳过边界路径 {contour_data['id']}，点数不足 ({len(b_points_3d)})。")
        
        print(f"直接偏置策略：添加了 {len(external_contours_3d)} 个外部轮廓和 {len(internal_contours_3d)} 个内部轮廓。")
        print(f"  为孔洞检测创建了 {len(shapely_internal_linestrings)} 个内部轮廓LineString对象。")
        if all_3d_boundary_linestrings:
            print(f"  为边界路径创建了 {len(all_3d_boundary_linestrings)} 个Shapely LineString对象。")
        else:
            print("  无边界路径用于Shapely邻近检查。")

        # proximity_threshold参数现在从函数参数传入，默认值0.15毫米

        # 2. 预先创建内部轮廓的2D多边形用于孔洞检测
        internal_polygons_2d = []
        if internal_contours_3d:
            print(f"  预先创建内部轮廓的孔洞检测多边形...")
            for internal_contour_data in internal_contours_3d:
                try:
                    internal_poly_2d = Polygon(internal_contour_data['points_2d'])
                    if not internal_poly_2d.is_valid:
                        internal_poly_2d = internal_poly_2d.buffer(0)
                    if internal_poly_2d.is_valid and not internal_poly_2d.is_empty:
                        # 为孔洞多边形添加一个安全缓冲区，与边界距离保持一致
                        safety_buffer = proximity_threshold  # 使用与边界相同的距离
                        buffered_hole_poly = internal_poly_2d.buffer(safety_buffer)
                        internal_polygons_2d.append(buffered_hole_poly)
                        print(f"    为内部轮廓 {internal_contour_data['id']} 创建了缓冲区为 {safety_buffer:.2f} 的孔洞多边形")
                except Exception as e:
                    print(f"    警告: 创建内部轮廓 {internal_contour_data['id']} 的2D多边形时出错: {e}")

        # 3. 生成直接偏置的填充路径
        offset_dir_axis = self.axis_index
        min_bound_offset_axis = self.mesh_bounds[0, offset_dir_axis]
        max_bound_offset_axis = self.mesh_bounds[1, offset_dir_axis]
        start_offset = min_bound_offset_axis + offset_from_bounds
        end_offset = max_bound_offset_axis - offset_from_bounds

        if start_offset >= end_offset:
            print(f"警告: 偏置范围无效 (start={start_offset:.3f}, end={end_offset:.3f})。无法生成填充。")
            return final_paths_list, []

        # 根据是否启用自适应密度，使用不同的切片位置生成策略
        if adaptive_density:
            # The old logic for choosing between ny_based and curvature_heuristic is now replaced by the iterative method.
            
            print(f"  启用自适应密度: 将使用迭代反馈方法。")

            # 修正: 计算正确的3D目标间距
            # 使用原始spacing计算公式：path_row_spacing = target_bead_width * 0.7
            # 保持原始间距计算，避免过度修正
            surface_angle_factor = 1.03  # 优化值，在1.0-1.05范围内，平衡精度和稳定性
            target_3d_spacing_objective = row_spacing * surface_angle_factor

            print(f"    间距修正: 2D行间距={row_spacing:.4f}mm -> 3D目标间距={target_3d_spacing_objective:.4f}mm (表面角度因子={surface_angle_factor})")
            
            main_boundary_for_iterative_slicer = None
            if external_contours_3d: 
                if external_contours_3d[0]['points_3d'] is not None and len(external_contours_3d[0]['points_3d']) >= 2:
                    external_boundary_points_2d = external_contours_3d[0]['points_2d']
                    if len(external_boundary_points_2d) >= 2:
                        main_boundary_for_iterative_slicer = LineString(external_boundary_points_2d)
            
            if main_boundary_for_iterative_slicer is None or not main_boundary_for_iterative_slicer.is_valid:
                print(f"    错误 (_create_direct_offset_paths): 无法为迭代切片确定有效的外部边界LineString。将回退到固定间距。")
                num_steps = int(np.floor((end_offset - start_offset) / row_spacing)) + 1
                slice_positions = [start_offset + i * row_spacing for i in range(num_steps)] if num_steps > 0 else []
            else:
                print(f"    使用迭代方法确定切片位置。主边界: {main_boundary_for_iterative_slicer.geom_type} (长度: {main_boundary_for_iterative_slicer.length:.2f})")
                print(f"    迭代切片将使用 {len(internal_polygons_2d)} 个内部2D缓冲孔洞多边形。")

                slice_positions = self.generate_adaptive_slice_positions_iterative(
                    start_offset=start_offset, 
                    end_offset=end_offset,
                    target_3d_spacing_objective=target_3d_spacing_objective,
                    offset_dir_axis=offset_dir_axis,
                    boundary_shapely_linestring=main_boundary_for_iterative_slicer, 
                    inner_contours_shapely_list=internal_polygons_2d, 
                    proximity_threshold=proximity_threshold, 
                    max_segment_length=max_segment_length,
                    min_delta_y_factor=iter_min_delta_y_factor,
                    max_delta_y_factor=iter_max_delta_y_factor,
                    tolerance_abs=iter_tolerance_abs,
                    iter_max_iterations_per_step=iter_max_iterations_per_step,
                    iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc
                )
        else:
            # 使用传统的固定间距切片
            num_steps = int(np.floor((end_offset - start_offset) / row_spacing)) + 1
            if num_steps <= 0:
                print(f"警告: 计算的偏置步数为零或负 ({num_steps})。无法生成填充。")
                return final_paths_list, []
            slice_positions = [start_offset + i * row_spacing for i in range(num_steps)]
            print(f"  固定间距模式: 从 {start_offset:.3f} 到 {end_offset:.3f}, 共 {len(slice_positions)} 个切片位置。")
        
        if not slice_positions:
            print(f"警告: 未能生成任何切片位置。")
            return final_paths_list, []
            
        print(f"  偏置轴 ({'X' if offset_dir_axis == 0 else 'Y'}): 将处理 {len(slice_positions)} 个切片位置")

        all_strips_data = []
        for slice_idx, current_offset_val in enumerate(slice_positions):
            plane_normal = np.zeros(3)
            plane_normal[offset_dir_axis] = 1.0
            plane_origin_base = self.mesh.centroid.copy()
            plane_origin_base[offset_dir_axis] = current_offset_val
            plane_origin = plane_origin_base

            try:
                path3d_slice = self.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
            except Exception as e:
                print(f"警告: 在偏置值 {current_offset_val:.3f} 进行切片时发生错误: {e}")
                path3d_slice = None
            
            polylines_in_slice = []
            if path3d_slice is not None and path3d_slice.discrete:
                polylines_in_slice.extend(path3d_slice.discrete)
            elif path3d_slice is not None and path3d_slice.vertices is not None and len(path3d_slice.vertices) >= self.min_points_req:
                raw_vertices_from_slice = path3d_slice.vertices
                sort_key_axis_for_slice = 1 - offset_dir_axis
                # Trimesh的section有时返回未排序的点，或多个不相连的段落作为一个列表。
                # 这是一个简化处理：假设排序能处理单个连续段。更复杂的断开检测可能需要。
                # TODO: 如果section返回多个不连续段，这里的排序和处理逻辑可能需要改进。
                # 例如，可以通过距离阈值将raw_vertices_from_slice分割成多个polylines_in_slice。
                # 目前，按扫描轴排序，并希望它形成一个（或多个可被后续逻辑处理的）合理顺序。
                indices = np.argsort(raw_vertices_from_slice[:, sort_key_axis_for_slice])
                sorted_vertices = raw_vertices_from_slice[indices]
                polylines_in_slice.append(sorted_vertices) # 将排序后的作为一个polyline处理

            for polyline_vertices_initial in polylines_in_slice:
                if len(polyline_vertices_initial) < self.min_points_req:
                    continue

                # --- 旧的初始修剪逻辑 (d_surf/2) 已被移除 ---
                # 原因: 后续的 _segment_strip_by_3d_intersections 和 
                # 对每个子段的 _trim_path_ends(..., self.d_surf / 2.0) 已覆盖此功能。
                # 直接使用原始切片点进行后续处理。
                points_after_dsurf_trim = polyline_vertices_initial
                # --- 旧的初始修剪逻辑结束 ---

                if len(points_after_dsurf_trim) < self.min_points_req: # 理论上这里不会因为移除了修剪而改变点数
                    continue
                
                # Interpolate the (potentially d_surf/2 trimmed) strip
                # 现在 points_after_dsurf_trim 就是 polyline_vertices_initial
                strip_candidate_points = self.interpolate_path_points(points_after_dsurf_trim, max_segment_length)
                if len(strip_candidate_points) < self.min_points_req:
                    continue
                
                # 使用批量法线计算优化性能
                strip_candidate_normals = self.get_surface_normals_batch(strip_candidate_points)

                # --- NEW FILL SEGMENT IDENTIFICATION LOGIC using helper method ---
                # Call the new helper method to get fill segments
                fill_sub_segments = self._segment_strip_by_3d_intersections(
                    strip_candidate_points, 
                    strip_candidate_normals, 
                    all_3d_boundary_linestrings
                )

                if fill_sub_segments:
                    for sub_segment_data in fill_sub_segments:
                        current_sub_points = sub_segment_data['points']
                        current_sub_normals = sub_segment_data['normals'] # Original normals for the sub-segment

                        # Trim both ends of this fill sub-segment
                        trimmed_sub_points = self._trim_path_ends(current_sub_points, self.d_surf / 2.0)

                        if trimmed_sub_points is not None and len(trimmed_sub_points) >= self.min_points_req:
                            # Normals need to be re-evaluated for the trimmed path
                            trimmed_sub_normals = self.get_surface_normals_batch(trimmed_sub_points)
                            
                            if trimmed_sub_normals is not None and len(trimmed_sub_normals) == len(trimmed_sub_points):
                                all_strips_data.append({
                                    'offset_val': current_offset_val,
                                    'points': trimmed_sub_points,
                                    'normals': trimmed_sub_normals,
                                    'slice_dir_axis_start_coord': trimmed_sub_points[0, 1 - offset_dir_axis]
                                })
                            # else:
                                # print(f"      警告: 修剪后的子段未能获取有效法线，跳过。点数: {len(trimmed_sub_points)}") # 可选
                        # else:
                            # print(f"      警告: 修剪后的子段点数不足 ({len(trimmed_sub_points) if trimmed_sub_points is not None else 'None'}) 或为None，跳过。原始点数: {len(current_sub_points)}") # 可选
                # --- END NEW FILL SEGMENT LOGIC (replaced old proximity filtering and hole avoidance) ---

            # 此处原有的segments_avoiding_holes 和 proximity filtering 逻辑已被新的基于交点的分段逻辑取代
            # 因此，那些代码块应该已经被移除，或者现在这个新的逻辑是唯一处理 strip_candidate_points 的地方

        if not all_strips_data:
            print("直接偏置策略：未能从截面生成任何路径条带（在修剪、插值和边界过滤后）。")
            print(f"  调试信息: 处理了 {len(slice_positions)} 个切片位置")
            return final_paths_list, []

        all_strips_data.sort(key=lambda s: (s['offset_val'], s['slice_dir_axis_start_coord']))

        # 增强: 添加路径生成统计信息
        strips_per_slice = len(all_strips_data) / len(slice_positions) if slice_positions else 0
        print(f"  直接偏置策略：共生成 {len(all_strips_data)} 个过滤后的3D路径条带/段。")
        print(f"  路径生成效率: {len(all_strips_data)}/{len(slice_positions)} = {strips_per_slice:.2f} 条带/切片")
        print(f"  现在尝试蛇形连接...")
        
        # --- Calculate and Print Actual 3D Spacing between Offset Layers ---
        # 恢复3D间距过滤逻辑，但使用更宽松的阈值
        # 原因：虽然迭代算法已经优化，但实际还存在路径条带过近的情况
        # 使用90%期望间距作为最小阈值，只移除真正过近的路径条带
        if all_strips_data:
            strips_by_offset_val = {}
            for strip_data_item in all_strips_data: # Renamed to avoid conflict with outer scope
                offset_val = strip_data_item['offset_val']
                if offset_val not in strips_by_offset_val:
                    strips_by_offset_val[offset_val] = []
                # Ensure 'points' key exists and is a numpy array with at least 1 point
                if 'points' in strip_data_item and isinstance(strip_data_item['points'], np.ndarray) and strip_data_item['points'].ndim == 2 and strip_data_item['points'].shape[0] > 0:
                    strips_by_offset_val[offset_val].append(strip_data_item['points'])

            sorted_unique_offset_vals = sorted(strips_by_offset_val.keys())

            print(f"\n  --- 实际3D间距分析 (层间，蛇形连接前) ---")
            if len(sorted_unique_offset_vals) < 2:
                print("    只有一个偏置层或没有足够的偏置层来进行间距分析。")
                pass # 如果只有一个偏置层，则没有间距可分析，但保持结构
            
            offset_values_to_remove = set() # 用于存储需要移除的偏置值
            # 优化阈值：使用目标间距的70%作为最小允许间距（更严格的质量标准）
            min_actual_3d_spacing_allowed = target_3d_spacing_objective * 0.1  # 70% of target 3D spacing
            print(f"    最小允许平均3D间距阈值 (target_3d_spacing*0.7): {min_actual_3d_spacing_allowed:.4f}mm")

            scan_axis = 1 - offset_dir_axis
            spacing_analysis_data = [] # 初始化用于存储间距分析数据的列表

            for k_offset_idx in range(len(sorted_unique_offset_vals) - 1): # Renamed loop variable
                offset_val_1 = sorted_unique_offset_vals[k_offset_idx]
                offset_val_2 = sorted_unique_offset_vals[k_offset_idx+1]

                strips_layer1 = strips_by_offset_val.get(offset_val_1, [])
                strips_layer2 = strips_by_offset_val.get(offset_val_2, [])

                if not strips_layer1 or not strips_layer2:
                    print(f"    跳过间距分析: ~{offset_val_1:.3f}mm 和 ~{offset_val_2:.3f}mm 之间的层数据不足。")
                    continue

                projected_step_dist = abs(offset_val_2 - offset_val_1)
                
                # 调用辅助函数来计算3D间距统计
                spacing_stats = self._calculate_actual_3d_spacing_between_strip_sets(
                    strips_layer1, strips_layer2, scan_axis, num_samples_on_strip1=10
                )

                if spacing_stats and spacing_stats['samples'] > 0:
                    avg_dist = spacing_stats['mean']
                    min_dist = spacing_stats['min']
                    max_dist = spacing_stats['max']
                    median_dist = spacing_stats['median']
                    std_dev_dist = spacing_stats['std']
                    num_samples_calc = spacing_stats['samples']
                    
                    error_mm = avg_dist - target_3d_spacing_objective # 计算误差

                    spacing_analysis_data.append({
                        'Offset1_mm': offset_val_1,
                        'Offset2_mm': offset_val_2,
                        'ProjectedStep_mm': projected_step_dist,
                        'Target3DSpacing_mm': target_3d_spacing_objective,
                        'ActualAvg3DSpacing_mm': avg_dist,
                        'Error_mm': error_mm,
                        'Min3DSpacing_mm': min_dist,
                        'Max3DSpacing_mm': max_dist,
                        'Median3DSpacing_mm': median_dist,
                        'StdDev3DSpacing_mm': std_dev_dist,
                        'NumSamples': num_samples_calc
                    })

                    # print(f"  偏移层间距: ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm (投影步长: {projected_step_dist:.4f}mm)")
                    # print(f"    样本数: {num_samples_calc}")
                    # print(f"    3D间距 -> 平均: {avg_dist:.4f}mm | 最小: {min_dist:.4f}mm | 最大: {max_dist:.4f}mm | 中位数: {median_dist:.4f}mm | 标准差: {std_dev_dist:.4f}mm")

                    if avg_dist < min_actual_3d_spacing_allowed and not np.isnan(avg_dist):
                        print(f"    警告: 偏移层 ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm 间的平均3D间距 {avg_dist:.4f}mm 过小 (小于阈值 {min_actual_3d_spacing_allowed:.4f}mm)。")
                        print(f"    将标记与偏置值 {offset_val_2:.3f}mm 相关的所有路径条带进行移除。")
                        offset_values_to_remove.add(offset_val_2)
                    elif np.isnan(avg_dist):
                         print(f"    警告: 偏移层 ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm 间的平均3D间距无法计算 (NaN)。")
                else:
                    # print(f"  偏移层间距: ~{offset_val_1:.3f}mm 与 ~{offset_val_2:.3f}mm (投影步长: {projected_step_dist:.4f}mm)")
                    # print(f"    未能计算有效的3D间距 (样本数: {spacing_stats['samples'] if spacing_stats else 0}).")
                    spacing_analysis_data.append({
                        'Offset1_mm': offset_val_1,
                        'Offset2_mm': offset_val_2,
                        'ProjectedStep_mm': projected_step_dist,
                        'Target3DSpacing_mm': target_3d_spacing_objective,
                        'ActualAvg3DSpacing_mm': float('nan'),
                        'Error_mm': float('nan'),
                        'Min3DSpacing_mm': float('nan'),
                        'Max3DSpacing_mm': float('nan'),
                        'Median3DSpacing_mm': float('nan'),
                        'StdDev3DSpacing_mm': float('nan'),
                        'NumSamples': spacing_stats['samples'] if spacing_stats else 0
                    })
            
            print("  --- 实际3D间距分析结束 ---")

            if offset_values_to_remove:
                print(f"  正在根据3D间距过滤移除 {len(offset_values_to_remove)} 个偏置值的路径条带...")
                original_strip_count_before_filter = len(all_strips_data)
                all_strips_data = [strip for strip in all_strips_data if strip['offset_val'] not in offset_values_to_remove]
                print(f"  过滤完成。移除了 {original_strip_count_before_filter - len(all_strips_data)} 个路径条带。剩余 {len(all_strips_data)} 个。")
                if not all_strips_data:
                    print("警告: 过滤后没有剩余路径条带可用于蛇形连接。")
            else:
                print(f"  无需移除路径条带，所有间距都满足要求。")
        else:
            spacing_analysis_data = []  # 如果没有路径条带，返回空的间距分析数据
        # --- End Calculate and Print Actual 3D Spacing ---

        # --- Path Strips Snake-like Connection ---
        t_start_snake_connection = time.time()
        max_connection_dist_factor = 2.5 
        connection_normal_similarity_threshold = 0.5 
        max_connection_length = max_connection_dist_factor * row_spacing
        
        current_long_path_points = [] 
        current_long_path_normals = []
        direction_for_next_strip = True 
        
        try:
            for strip_idx, current_strip_data in enumerate(all_strips_data):
                strip_pts_orig = current_strip_data['points']
                strip_nmls_orig = current_strip_data['normals']

                processed_strip_pts = np.copy(strip_pts_orig) 
                processed_strip_nmls = np.copy(strip_nmls_orig)

                if not direction_for_next_strip: 
                    processed_strip_pts = processed_strip_pts[::-1] 
                    processed_strip_nmls = processed_strip_nmls[::-1] 
                
                if not current_long_path_points: 
                    current_long_path_points.extend(list(processed_strip_pts))
                    current_long_path_normals.extend(list(processed_strip_nmls))
                else: 
                    prev_strip_end_point = np.array(current_long_path_points[-1])
                    prev_strip_end_normal = np.array(current_long_path_normals[-1])
                    next_strip_start_point = processed_strip_pts[0]
                    next_strip_start_normal = processed_strip_nmls[0]

                    connection_possible = True 
                    connection_vector = next_strip_start_point - prev_strip_end_point 
                    connection_distance = np.linalg.norm(connection_vector)

                    if connection_distance > max_connection_length: 
                        connection_possible = False
                    
                    if connection_possible: 
                        norm_prev_mag = np.linalg.norm(prev_strip_end_normal)
                        norm_next_mag = np.linalg.norm(next_strip_start_normal)
                        if norm_prev_mag > 1e-6 and norm_next_mag > 1e-6: 
                            norm_prev_unit = prev_strip_end_normal / norm_prev_mag
                            norm_next_unit = next_strip_start_normal / norm_next_mag
                            dot_product = np.dot(norm_prev_unit, norm_next_unit) 
                            if dot_product < connection_normal_similarity_threshold: 
                                connection_possible = False
                        else: 
                            connection_possible = False 
                    
                    if connection_possible and connection_distance > 1e-6: 
                        ray_origin_offset = 1e-4 
                        ray_length_reduction = 2e-4 
                        ray_dir_normalized = np.array([0.0,0.0,0.0]) 
                        if connection_distance > 1e-9: 
                             ray_dir_normalized = connection_vector / connection_distance

                        actual_ray_origin = prev_strip_end_point + ray_dir_normalized * ray_origin_offset
                        actual_max_ray_distance = connection_distance - ray_length_reduction 
                        
                        if actual_max_ray_distance > 1e-6: 
                            locations, _, _ = self.mesh.ray.intersects_location(
                                ray_origins=[actual_ray_origin], ray_directions=[ray_dir_normalized]
                            )
                            if len(locations) > 0: 
                                closest_hit_distance = np.linalg.norm(locations[0] - actual_ray_origin)
                                if closest_hit_distance < actual_max_ray_distance: 
                                    connection_possible = False 
                        
                    if connection_possible: 
                        current_long_path_points.extend(list(processed_strip_pts)) 
                        current_long_path_normals.extend(list(processed_strip_nmls))
                    else: 
                        if len(current_long_path_points) >= self.min_points_req:
                            final_paths_list.append((np.array(current_long_path_points), 
                                                     np.array(current_long_path_normals), 
                                                     False, fill_segment_id_counter))
                            fill_segment_id_counter -=1
                        
                        current_long_path_points = list(processed_strip_pts) 
                        current_long_path_normals = list(processed_strip_nmls)
                
                direction_for_next_strip = not direction_for_next_strip

            # Add the last accumulated long path
            if current_long_path_points and len(current_long_path_points) >= self.min_points_req:
                final_paths_list.append((np.array(current_long_path_points), 
                                         np.array(current_long_path_normals), 
                                         False, fill_segment_id_counter))
        except Exception as e_snake: # Correctly indented except block
            print(f"!!!!!!!!!!!!!!!!! 错误: 在蛇形连接逻辑中发生异常 !!!!!!!!!!!!!!!!!!")
            print(f"错误类型: {type(e_snake)}")
            print(f"错误信息: {e_snake}")
            import traceback
            print("Traceback:")
            traceback.print_exc()
            print(f"发生异常时，current_long_path_points 中有 {len(current_long_path_points)} 个点。")
            if 'strip_idx' in locals(): # Check if strip_idx is defined
                 print(f"异常可能发生在处理 strip_idx = {strip_idx} 时。")

        # print(f"--- 直接偏置策略 蛇形连接部分耗时: {time.time() - t_start_snake_connection:.4f} 秒 ---")
        num_fill_paths_added = sum(1 for _, _, is_b, _ in final_paths_list if not is_b)
        print(f"  直接偏置策略：蛇形连接后共添加 {num_fill_paths_added} 个填充路径段。")
        
        # 执行综合间距质量分析（在所有3D间距计算完成后）
        if spacing_analysis_data:
            quality_metrics = self._analyze_spacing_quality_comprehensive(
                spacing_analysis_data, target_3d_spacing_objective
            )

            # 将质量指标添加到性能统计中
            self._performance_stats.update({
                'final_quality_score': quality_metrics.get('quality_score', 0),
                'target_achievement_rate': quality_metrics.get('target_achievement_rate', 0),
                'rms_error_percentage': quality_metrics.get('rms_error_percentage', 100),
                'spacing_warnings': quality_metrics.get('spacing_warnings', 999)
            })

        # 返回实际的间距分析数据（在上面的3D间距分析中生成）
        return final_paths_list, spacing_analysis_data

    def save_paths_to_gcode(self, paths_data, output_gcode_file, 
                            klipper_mode=False, 
                            feed_rate_print=600, feed_rate_travel=3000, 
                            target_extruder_temp=210, target_bed_temp=60,
                            filament_diameter=1.75, extrusion_width=0.4, layer_height=0.2, 
                            retraction_amount=1.0, retraction_feedrate=2700, 
                            extrusion_multiplier=1.0,
                            normal_hop_distance=1.0, 
                            clearance_above_model_max=5.0, 
                            rotation_feed_rate=3000, 
                            enable_rotation_axes=True # 是否启用ABC旋转轴输出
                            ):
        """将路径数据保存为G-code文件，采用高级抬刀逻辑（抬至模型最大Z之上），并将XYZ原点调整。"""
        if not paths_data:
            print("警告: save_paths_to_gcode 没有路径数据可写。")
            return

        # 根据模式设置G-code注释
        mode_description = "6-Axis G-code Mode (with ABC rotation axes)" if enable_rotation_axes else "Traditional G-code Mode (XYZ coordinates only)"

        # 计算所有路径点的整体边界，用于确定偏移量和安全Z高度
        min_x_overall, min_y_overall, min_z_overall = float('inf'), float('inf'), float('inf')
        max_x_overall, max_y_overall, max_z_overall = -float('inf'), -float('inf'), -float('inf') 
        has_any_points = False # 标记是否有任何有效点

        for points_segment, _, _, _ in paths_data:
            if points_segment is not None and len(points_segment) > 0:
                has_any_points = True
                current_min_x, current_min_y, current_min_z = np.min(points_segment[:, 0]), np.min(points_segment[:, 1]), np.min(points_segment[:, 2])
                current_max_x, current_max_y, current_max_z = np.max(points_segment[:, 0]), np.max(points_segment[:, 1]), np.max(points_segment[:, 2]) 
                min_x_overall = min(min_x_overall, current_min_x)
                min_y_overall = min(min_y_overall, current_min_y)
                min_z_overall = min(min_z_overall, current_min_z)
                max_x_overall = max(max_x_overall, current_max_x)
                max_y_overall = max(max_y_overall, current_max_y)
                max_z_overall = max(max_z_overall, current_max_z)
        
        x_offset, y_offset, z_offset = 0.0, 0.0, 0.0 # 初始化XYZ偏移
        absolute_safe_z_level_gcode = 20.0 # 默认的绝对安全Z高度 (G-code坐标系)

        if has_any_points:
            center_x, center_y = (min_x_overall + max_x_overall) / 2.0, (min_y_overall + max_y_overall) / 2.0
            x_offset = 100.0 - center_x 
            y_offset = 100.0 - center_y 
            z_offset = 0 # Original Z values are maintained by not applying a Z offset
            absolute_safe_z_level_gcode = max_z_overall + z_offset + clearance_above_model_max # z_offset is 0.0 here
        else:
            print("警告: G-code: 未找到有效点确定偏移。无XYZ偏移输出，安全Z高度将使用默认值。")

        with open(output_gcode_file, 'w', encoding='utf-8') as f:
            f.write(f"; G-code Generated by DirectProjectionSlicer (Advanced Hop Logic)\n")
            f.write(f"; Mode: {mode_description}\n")
            f.write(f"; Original Mesh: {self.mesh_path}\n")
            f.write(f"; XY offset to center model approx at (100,100). Original Z values are maintained.\\n")
            f.write(f"; Applied X_offset: {x_offset:.4f}, Y_offset: {y_offset:.4f}, Z_offset: {z_offset:.4f}\n")
            f.write(f"; Normal Hop Distance: {normal_hop_distance:.2f}mm, Max Clearance Above Model: {clearance_above_model_max:.2f}mm (Target Z for hop: {absolute_safe_z_level_gcode:.4f})\n")
            
            # 添加打印参数信息（无论何种模式都显示）
            f.write(f"; Filament Diameter: {filament_diameter:.2f}mm, Extrusion Width: {extrusion_width:.2f}mm, Layer Height: {layer_height:.2f}mm\n")
            f.write(f"; Retraction: {retraction_amount:.2f}mm @ {retraction_feedrate}mm/min\n")
            f.write(f"; Extrusion Multiplier: {extrusion_multiplier*100:.1f}%\n")
            
            if klipper_mode:
                f.write(f"; Klipper Mode Activated\n")

            
            f.write(f"G21 ; Set units to millimeters\n")
            f.write(f"G90 ; Use absolute XYZ positioning\n")
            f.write(f"M83 ; Use relative E distances (safer for Klipper extrusion calculation with SET_EXTRUDER_ROTATION_DISTANCE)\n" if klipper_mode else "M82 ; Use absolute E distances\n")
            
            if klipper_mode:
                f.write(f"PRINT_START BED_TEMP={target_bed_temp} EXTRUDER_TEMP={target_extruder_temp}\n")
            else: 
                f.write(f"M104 S{target_extruder_temp} ; Set extruder temperature (no wait)\n")
                f.write(f"M140 S{target_bed_temp} ; Set bed temperature (no wait)\n")
                f.write(f"M109 S{target_extruder_temp} ; Wait for extruder to reach temperature\n")
                f.write(f"M190 S{target_bed_temp} ; Wait for bed to reach temperature\n")

            initial_safe_z = absolute_safe_z_level_gcode + 5.0 

            current_x, current_y, current_z = 0, 0, initial_safe_z 
            current_a, current_b, current_c = 0.0, 0.0, 0.0 
            current_e = 0.0 
            
            if enable_rotation_axes:
                f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{current_z:.4f} A{current_a:.3f} B{current_b:.3f} C{current_c:.3f} F{feed_rate_travel} ; Initial position and orientation\n")
            else:
                f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{current_z:.4f} F{feed_rate_travel} ; Initial position (Traditional mode)\n")
            f.write(f"; -- Path Data Start --\n\n")
            
            filament_area = math.pi * (filament_diameter / 2.0)**2
            extrusion_per_mm = 0 
            if filament_area > 0: 
                extrusion_per_mm = (extrusion_width * layer_height) / filament_area

            last_normal_for_hop = None 

            for path_idx, (original_points_segment, normals_segment, is_boundary, segment_id) in enumerate(paths_data):
                if original_points_segment is None or len(original_points_segment) < 1 or \
                   (normals_segment is None or len(normals_segment) != len(original_points_segment)): 
                    print(f"  G-code: Skipping invalid or normal-mismatched path segment (ID: {segment_id})")
                    continue
                
                current_segment_points_gcode = np.copy(original_points_segment)
                current_segment_points_gcode[:, 0] += x_offset
                current_segment_points_gcode[:, 1] += y_offset
                current_segment_points_gcode[:, 2] += z_offset

                path_type_str = "BOUNDARY" if is_boundary else f"FILL_SEGMENT_{segment_id}"
                f.write(f"; ---- Preparing Path Segment {path_idx + 1}, OriginalID: {segment_id}, Type: {path_type_str}, Points: {len(current_segment_points_gcode)} ----\n")
                
                target_print_start_coord = current_segment_points_gcode[0] 
                target_print_start_normal_raw = normals_segment[0] 
                pre_approach_point_calculated = target_print_start_coord 
                if normal_hop_distance > 0:
                    norm_val_next_start = np.linalg.norm(target_print_start_normal_raw)
                    if norm_val_next_start > 1e-6:
                        normalized_target_start_normal = target_print_start_normal_raw / norm_val_next_start
                        tool_axis_for_approach = -normalized_target_start_normal if self.inward_normals else normalized_target_start_normal
                        offset_vector_for_pre_approach = tool_axis_for_approach * normal_hop_distance
                        pre_approach_point_calculated = target_print_start_coord + offset_vector_for_pre_approach
                    else:
                        f.write(f"; WARNING: Start normal for segment {segment_id} is zero vector. Will use direct approach for reorientation.\n")
                
                # 2. In current safe Z height move to XY of pre-approach point
                if not (np.allclose(current_x, pre_approach_point_calculated[0]) and np.allclose(current_y, pre_approach_point_calculated[1])):
                    # Remove E parameter for moving to pre-approach XY
                    f.write(f"G0 X{pre_approach_point_calculated[0]:.4f} Y{pre_approach_point_calculated[1]:.4f} Z{current_z:.4f} F{feed_rate_travel} ; Move to Pre-Approach XY (Safe Z)\n") # Z remains current, only XY moves
                    current_x, current_y = pre_approach_point_calculated[0], pre_approach_point_calculated[1]

                # 3. Lower to Z height of pre-approach point
                if not np.allclose(current_z, pre_approach_point_calculated[2]):
                    # Remove E parameter for lowering to pre-approach Z
                    f.write(f"G0 X{current_x:.4f} Y{current_y:.4f} Z{pre_approach_point_calculated[2]:.4f} F{feed_rate_travel} ; Lower to Pre-Approach Z\n") # XY remains current, only Z moves
                    current_z = pre_approach_point_calculated[2]
                
                # 4. Adjust tool orientation at pre-approach point
                try:
                    target_start_a, target_start_b, target_start_c = self.normal_to_rpy_degrees(target_print_start_normal_raw)
                except Exception as e_reorient:
                    print(f"  G-code: Error calculating RPY for segment {segment_id} start: {e_reorient}. Using A0 B0 C0.\n")
                    target_start_a, target_start_b, target_start_c = 0.0, 0.0, 0.0 # Use default orientation on error
                
                if not (np.allclose(current_a, target_start_a) and np.allclose(current_b, target_start_b) and np.allclose(current_c, target_start_c)):
                    # Remove E parameter for adjusting orientation
                    if enable_rotation_axes:
                        f.write(f"G0 A{target_start_a:.3f} B{target_start_b:.3f} C{target_start_c:.3f} F{rotation_feed_rate} ; Reorient at Pre-Approach Point\n")
                    else:
                        f.write(f"G0 F{rotation_feed_rate} ; Reorient at Pre-Approach Point (Traditional mode)\n")
                    current_a, current_b, current_c = target_start_a, target_start_b, target_start_c
                
                # 5. (Optional) Prime/Unretract
                if klipper_mode and retraction_amount > 0: # Klipper uses M83 (relative extrusion)
                    f.write(f"G1 E{retraction_amount:.5f} F{retraction_feedrate} ; Prime (Klipper M83)\n")
                elif not klipper_mode and retraction_amount > 0: # Standard firmware uses M82 (absolute extrusion)
                     current_e += retraction_amount # Increase extrusion amount
                     f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Prime (M82)\n")

                # --- Start printing current path segment ---
                f.write(f"; Printing Path Segment {path_idx + 1}\n")
                for i in range(len(current_segment_points_gcode)):
                    p_coord = current_segment_points_gcode[i] # Current point coordinates (already offset)
                    p_normal = normals_segment[i] # Current point normal (original)
                    try:
                        roll_deg, pitch_deg, yaw_deg = self.normal_to_rpy_degrees(p_normal)
                    except Exception as e_rpy:
                        print(f"  G-code: Error calculating normal RPY for point {i} (Segment ID {segment_id}): {e_rpy}. Using previous angles.\n")
                        roll_deg, pitch_deg, yaw_deg = current_a, current_b, current_c # Maintain previous orientation on error

                    gcode_line_parts = [f"G1 X{p_coord[0]:.4f} Y{p_coord[1]:.4f} Z{p_coord[2]:.4f}"]
                    
                    # --- Calculate Extrusion ---
                    if i == 0: # First point (landing move), no extrusion
                        pass # No extrusion operation
                    else: # Subsequent points (i > 0), calculate and add extrusion
                        if klipper_mode: # Klipper (M83 relative extrusion)
                            prev_print_point = current_segment_points_gcode[i-1]
                            segment_length = math.sqrt(
                                (p_coord[0] - prev_print_point[0])**2 +
                                (p_coord[1] - prev_print_point[1])**2 +
                                (p_coord[2] - prev_print_point[2])**2
                            )
                            delta_e = 0.0
                            if segment_length > 1e-9:
                                # 简化：由于slope_compensation_exponent=0，补偿因子总是1.0
                                delta_e = segment_length * extrusion_per_mm * extrusion_multiplier
                            
                            if delta_e > 1e-9: # Only add E parameter if extrusion amount is significant
                                gcode_line_parts.append(f"E{delta_e:.5f}")
                        
                        else: # Standard firmware (M82 absolute extrusion)
                            prev_print_point = current_segment_points_gcode[i-1]
                            segment_length = math.sqrt(np.sum((p_coord - prev_print_point)**2))
                            if segment_length > 1e-9: 
                               current_e += segment_length * extrusion_per_mm * extrusion_multiplier 
                            gcode_line_parts.append(f"E{current_e:.5f}")

                    if enable_rotation_axes:
                        gcode_line_parts.append(f"A{roll_deg:.3f} B{pitch_deg:.3f} C{yaw_deg:.3f}") # Add orientation
                    # 传统模式下不添加ABC角度，保持代码整洁
                    gcode_line_parts.append(f"F{feed_rate_print}") # Add print speed
                    f.write(" ".join(gcode_line_parts) + "\n")

                    # Update current state
                    current_x, current_y, current_z = p_coord[0], p_coord[1], p_coord[2]
                    current_a, current_b, current_c = roll_deg, pitch_deg, yaw_deg
                    last_normal_for_hop = p_normal # Save current normal for next hop
                
                # --- Advanced Hop after finishing current path segment ---
                f.write(f"; Path Segment {path_idx + 1} Print End\n")
                f.write(f"; Starting Advanced Hop from end of Segment {segment_id}\n")
                # 1. Retract
                if klipper_mode and retraction_amount > 0:
                    f.write(f"G1 E{-retraction_amount:.5f} F{retraction_feedrate} ; Retract before hop (Klipper M83)\n")
                elif not klipper_mode and retraction_amount > 0: 
                    current_e -= retraction_amount # Decrease extrusion amount
                    f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Retract before hop (M82)\n")

                # 2. Hop along tool axis (normal direction hop)
                hop_exit_point = np.array([current_x, current_y, current_z]) # Record point after normal hop
                if normal_hop_distance > 0 and last_normal_for_hop is not None:
                    norm_val = np.linalg.norm(last_normal_for_hop)
                    if norm_val > 1e-6:
                        # Tool axis direction for hop: if normals inward, tool axis is -normal; else normal.
                        tool_axis_direction_for_hop = -last_normal_for_hop if self.inward_normals else last_normal_for_hop
                        tool_axis_unit = tool_axis_direction_for_hop / norm_val
                        hop_dx, hop_dy, hop_dz = tool_axis_unit * normal_hop_distance
                        target_x_norm_hop, target_y_norm_hop, target_z_norm_hop = current_x + hop_dx, current_y + hop_dy, current_z + hop_dz
                        # Remove E parameter for tool axis normal hop
                        if enable_rotation_axes:
                            f.write(f"G1 X{target_x_norm_hop:.4f} Y{target_y_norm_hop:.4f} Z{target_z_norm_hop:.4f} A{current_a:.3f} B{current_b:.3f} C{current_c:.3f} F{feed_rate_travel} ; Hop along tool axis normal\n")
                        else:
                            f.write(f"G1 X{target_x_norm_hop:.4f} Y{target_y_norm_hop:.4f} Z{target_z_norm_hop:.4f} F{feed_rate_travel} ; Hop along tool axis normal (Traditional mode)\n")
                        current_x, current_y, current_z = target_x_norm_hop, target_y_norm_hop, target_z_norm_hop
                        hop_exit_point = np.array([current_x, current_y, current_z])
                    else:
                        f.write(f"; WARNING: Normal for hop is zero vector, skipping normal direction hop.\n")
                
                # 3. Return tool orientation to vertical (A0 B0)
                if not (np.allclose(current_a, 0) and np.allclose(current_b, 0)): # C-axis (Yaw) usually maintained or path-dependent
                    # Remove E parameter for returning tool to vertical
                    if enable_rotation_axes:
                        f.write(f"G0 A0.000 B0.000 C{current_c:.3f} F{rotation_feed_rate} ; Return tool to vertical (A, B axes, maintain C)\n") # Maintain C-axis
                    else:
                        f.write(f"G0 F{rotation_feed_rate} ; Return tool to vertical (Traditional mode)\n")
                    current_a, current_b = 0.0, 0.0
                
                # 4. Z-axis hop to absolute safe height
                if not np.allclose(current_z, absolute_safe_z_level_gcode):
                    # Remove E parameter for Z-axis secondary hop to absolute safe height
                    f.write(f"G0 Z{absolute_safe_z_level_gcode:.4f} F{feed_rate_travel} ; Z-axis secondary hop to absolute safe height\n")
                    current_z = absolute_safe_z_level_gcode
                
                f.write(f"; Advanced hop sequence for segment {segment_id} complete. Preparing approach for next segment.\n\n")
            
            f.write(f"; -- Path Data End --\n")
            if klipper_mode:
                if retraction_amount > 0 and has_any_points: # If content was printed, perform final retraction
                     f.write(f"G1 E{-retraction_amount:.5f} F{retraction_feedrate} ; Final Retraction (Klipper M83)\n")
                f.write(f"PRINT_END ; Call Klipper PRINT_END macro\n")
            else: # Standard firmware end sequence
                if retraction_amount > 0 and has_any_points: 
                     current_e -= retraction_amount
                     f.write(f"G1 E{current_e:.5f} F{retraction_feedrate} ; Final Retraction\n")
                f.write(f"M104 S0 ; Turn off extruder heater\n")
                f.write(f"M140 S0 ; Turn off bed heater\n")
                f.write(f"G91 ; Relative positioning\n")
                f.write(f"G1 Z10 F3000 ; Raise Z axis by 10mm\n")
                f.write(f"G90 ; Absolute positioning\n")
                f.write(f"G28 X0 Y0 ; Home XY axes\n")
                f.write(f"M84 ; Disable stepper motors\n")
                f.write(f"M2 ; Program End\n")

        print(f"G-code saved to {output_gcode_file}")

    def get_projected_boundary_contours(self, boundary_path_id_start=1):
        """
        获取所有边界路径，将其投影到XOY平面，并返回路径数据。
        返回:
        list_of_paths_data: 每个元素是字典 {'points_2d':二维点数组, 'points_3d':三维点数组, 'is_boundary':布尔值, 'id':路径ID}
        """
        raw_boundary_edges = self.get_boundary_edges()
        if not raw_boundary_edges:
            print("未找到边界边用于投影轮廓。")
            return []
        
        sorted_edge_paths_tuples = self.sort_boundary_edges(raw_boundary_edges)
        if not sorted_edge_paths_tuples:
            print("排序后未找到连续的边界路径用于投影。")
            return []

        projected_contours_data = []
        current_boundary_id = boundary_path_id_start
        for single_path_edges_tuples in sorted_edge_paths_tuples:
            if not single_path_edges_tuples: continue
            ordered_vertices_indices = []
            if len(single_path_edges_tuples) == 1: # 单一边构成路径
                v1, v2 = single_path_edges_tuples[0]
                ordered_vertices_indices.extend([v1, v2])
            else:
                # 从边重建顶点顺序
                # 这个简化逻辑假设 sort_boundary_edges 返回的边在某种程度上是有序的
                path_verts_temp = [single_path_edges_tuples[0][0], single_path_edges_tuples[0][1]]
                for i_edge in range(1, len(single_path_edges_tuples)):
                    e_v1, e_v2 = single_path_edges_tuples[i_edge]
                    if path_verts_temp[-1] == e_v1:
                        path_verts_temp.append(e_v2)
                    elif path_verts_temp[-1] == e_v2:
                        path_verts_temp.append(e_v1)
                    elif path_verts_temp[0] == e_v1: # 路径可能需要从另一端开始构建
                         path_verts_temp.insert(0, e_v2)
                    elif path_verts_temp[0] == e_v2:
                         path_verts_temp.insert(0, e_v1)
                    else: # 简单追加/前插无法处理的不连续或复杂连接
                        print(f"警告: 边界路径 {current_boundary_id} 顶点连接复杂，可能不完整。边: ({e_v1}, {e_v2}), 当前路径尾点: {path_verts_temp[-1]}")
                        # 后备方案: 如果是孤立的，只添加其中一个点，或者可以尝试更复杂的图遍历
                        # 目前，我们假设 sort_boundary_edges 提供的是基本首尾相连的段落
                        path_verts_temp.append(e_v1) # 任意选择
                        path_verts_temp.append(e_v2)

                ordered_vertices_indices = path_verts_temp
            
            # 去除连续重复的顶点索引
            unique_ordered_vertices = [ordered_vertices_indices[0]]
            for k_idx in range(1, len(ordered_vertices_indices)):
                if ordered_vertices_indices[k_idx] != ordered_vertices_indices[k_idx-1]:
                    unique_ordered_vertices.append(ordered_vertices_indices[k_idx])

            if len(unique_ordered_vertices) < self.min_points_req:
                print(f"边界路径 {current_boundary_id} 点数不足 ({len(unique_ordered_vertices)})，跳过投影。")
                continue
            
            boundary_points_3d = self.mesh.vertices[unique_ordered_vertices]
            projected_points_2d = boundary_points_3d[:, :2] # 投影到XOY平面
            
            projected_contours_data.append({
                'points_2d': projected_points_2d, 
                'points_3d': boundary_points_3d, 
                'is_boundary': True, 
                'id': current_boundary_id
            })
            print(f"已投影边界路径 {current_boundary_id}，包含 {len(projected_points_2d)} 个二维点。")
            current_boundary_id += 1
            
        return projected_contours_data

    def create_projected_fill_paths(self, row_spacing, 
                                    offset_distance=None, 
                                    max_segment_length=None, 
                                    strategy='direct_offset', # 路径生成策略
                                    projection_fill_pattern='raster', # 新增参数，用于投影策略
                                    proximity_threshold=0.15, # 与边界和孔洞的最小距离 (毫米)
                                    adaptive_density=True, # 自适应密度控制 (仅适用于direct_offset策略)
                                    # Removed old adaptive parameters like curvature_sensitivity, min_spacing_factor, max_spacing_factor for direct_offset
                                    # These are now handled by the iterative parameters if adaptive_density is on for direct_offset
                                    iter_min_delta_y_factor=0.05,
                                    iter_max_delta_y_factor=2.0,
                                    iter_tolerance_abs=0.1,
                                    iter_max_iterations_per_step=15,
                                    iter_num_samples_for_spacing_calc=7
                                    ):
        """ 
        主流程：根据选择的策略生成3D打印路径。
        
        strategy: 路径生成策略，当前支持 'direct_offset' 和 'projection'。
        projection_fill_pattern: 投影策略的2D填充模式 ('raster' 或 'concentric')。
        proximity_threshold: (对于direct_offset) 主要影响迭代切片器中孔洞的处理。
                           (对于projection) 当前未使用。
        adaptive_density: (仅用于direct_offset) 是否启用通过迭代方法实现的自适应密度控制。
        iter_min_delta_y_factor, iter_max_delta_y_factor, iter_tolerance_abs, 
        iter_max_iterations_per_step, iter_num_samples_for_spacing_calc: 
            (仅用于direct_offset且adaptive_density=True) 控制迭代自适应切片行为的参数。
        
        返回:
        final_paths_list: 包含所有路径段的列表 [(points, normals, is_boundary, segment_id), ...]
        spacing_data: (仅用于direct_offset) 包含间距分析数据的列表。
        """
        print(f"\n--- 开始路径生成，策略: '{strategy}' ---")
        # Removed print(f"  使用奇偶规则判断材料内部: {'是' if use_even_odd_rule else '否'}")

        if strategy.lower() == 'direct_offset': # 直接偏置策略
            # 实际边界内缩量，如果未提供则默认为行距的一半
            actual_offset_from_bounds = offset_distance if offset_distance is not None else row_spacing / 2.0
            paths_list, spacing_data = self._create_direct_offset_paths(
                row_spacing=row_spacing,
                offset_from_bounds=actual_offset_from_bounds,
                max_segment_length=max_segment_length,
                proximity_threshold=proximity_threshold,
                adaptive_density=adaptive_density,
                # Removed old adaptive parameters from call
                # min_spacing_factor=min_spacing_factor,
                # use_normal_component_method=use_normal_component_method,
                # ny_calculation_strategy=ny_calculation_strategy,
                # Pass new iteration parameters
                iter_min_delta_y_factor=iter_min_delta_y_factor,
                iter_max_delta_y_factor=iter_max_delta_y_factor,
                iter_tolerance_abs=iter_tolerance_abs,
                iter_max_iterations_per_step=iter_max_iterations_per_step,
                iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc
                # Removed use_even_odd_rule=use_even_odd_rule
            )
            return paths_list, spacing_data # 返回路径列表和间距数据
        
        elif strategy.lower() == 'projection': # 投影策略
            print(f"  使用投影策略，2D填充模式: '{projection_fill_pattern}'")
            all_boundary_contour_info = self.get_projected_boundary_contours() # 获取投影的2D边界轮廓
            if not all_boundary_contour_info:
                print("未能获取任何边界轮廓 (投影策略)，无法继续。")
                return [], [] # Return tuple like direct_offset
            
            external_contours = []
            # internal_contours_associated_with_external = {} # Store internal contours per external one

            for contour_data in all_boundary_contour_info:
                contour_2d = contour_data['points_2d']
                try:
                    contour_poly = Polygon(contour_2d)
                    if not contour_poly.is_valid:
                        contour_poly = contour_poly.buffer(0)
                except Exception as e:
                    print(f"警告: 处理轮廓 {contour_data['id']} 时出错: {e}")
                    continue
                
                is_internal = False
                for other_data in all_boundary_contour_info:
                    if other_data['id'] == contour_data['id']: continue
                    try:
                        other_poly = Polygon(other_data['points_2d'])
                        if not other_poly.is_valid: other_poly = other_poly.buffer(0)
                        if other_poly.contains(contour_poly):
                            is_internal = True
                            # Associate internal contour with its containing external contour
                            # Ensure 'inner_contours' list exists for the external contour_data
                            if 'inner_contours' not in other_data:
                                other_data['inner_contours'] = []
                            # Check for duplicates before adding
                            is_duplicate_inner = False
                            for existing_inner in other_data['inner_contours']:
                                if np.array_equal(existing_inner, contour_2d):
                                    is_duplicate_inner = True
                                    break
                            if not is_duplicate_inner:
                                other_data['inner_contours'].append(contour_2d)
                            break 
                    except Exception as e:
                        print(f"警告: 比较轮廓 {contour_data['id']} 和 {other_data['id']} 时出错: {e}")
                        continue
                if not is_internal:
                    external_contours.append(contour_data)
            
            print(f"共识别出 {len(external_contours)} 个外部轮廓。")
            
            final_paths_for_all_contours = []
            spacing_analysis_data_projection = [] # 为投影策略初始化间距分析数据列表
            
            # 新增：保存所有轮廓的2D填充数据用于后续可视化
            all_2d_fill_data = []
            all_projection_connections = []
            
            for b_data in all_boundary_contour_info:
                b_points_3d = b_data['points_3d']
                if len(b_points_3d) >= self.min_points_req:
                    # Use batch normal calculation if available and preferred
                    b_normals_3d = self.get_surface_normals_batch(b_points_3d)
                    if b_normals_3d is None or len(b_normals_3d) != len(b_points_3d):
                        print(f"警告: 边界路径 {b_data['id']} 法线计算失败，将逐点计算。")
                        b_normals_3d = np.array([self.get_surface_normal_at_point(p) for p in b_points_3d])
                    final_paths_for_all_contours.append((b_points_3d, b_normals_3d, True, b_data['id']))
                else:
                    print(f"边界路径 {b_data['id']} 点数不足 ({len(b_points_3d)})，跳过。")

            base_fill_segment_id_start = -10000 
            id_step_per_contour_fill = 10000
            target_3d_row_spacing = row_spacing 
            target_3d_offset_for_adjustment_calc = offset_distance if offset_distance is not None else target_3d_row_spacing / 2.0

            for contour_index, ex_contour_data in enumerate(external_contours):
                print(f"\n--- (投影策略) 处理外部轮廓 {ex_contour_data['id']} (索引 {contour_index}) 的填充 ---")
                current_contour_2d_points = ex_contour_data['points_2d']
                if len(current_contour_2d_points) < 3:
                    print(f"轮廓 {ex_contour_data['id']} 点数不足，无法生成2D填充。")
                    continue

                inner_contours_for_this_external = ex_contour_data.get('inner_contours', [])
                if inner_contours_for_this_external:
                    print(f"  轮廓 {ex_contour_data['id']} 将使用 {len(inner_contours_for_this_external)} 个内部孔洞进行填充排除。")

                # Check if _calculate_adjusted_spacing_parameters exists, if not, use fixed spacing
                adjusted_2d_row_spacing = target_3d_row_spacing # 默认值
                if hasattr(self, '_calculate_adjusted_spacing_parameters'):
                    adjusted_2d_row_spacing, adjusted_2d_offset_for_fill = self._calculate_adjusted_spacing_parameters(
                        current_contour_2d_points,
                        target_3d_row_spacing,
                        target_3d_offset_for_adjustment_calc 
                    )
                else:
                    print("警告: _calculate_adjusted_spacing_parameters 方法未找到，将使用固定的2D间距和偏移。")
                    # adjusted_2d_row_spacing 保持默认值
                    adjusted_2d_offset_for_fill = offset_distance if offset_distance is not None else row_spacing / 2.0
                
                fill_points_2d, connection_types, offset_polygon_shapely = None, None, None
                if projection_fill_pattern.lower() == 'raster':
                    if not hasattr(self, 'generate_2d_raster_fill'):
                        print("错误: generate_2d_raster_fill 方法未找到!")
                        continue
                    fill_points_2d, connection_types, offset_polygon_shapely = self.generate_2d_raster_fill(
                        current_contour_2d_points, adjusted_2d_row_spacing, 
                        offset_distance=adjusted_2d_offset_for_fill, 
                        max_segment_length=max_segment_length,
                        inner_contours_list=inner_contours_for_this_external
                    )
                elif projection_fill_pattern.lower() == 'concentric':
                    if not hasattr(self, 'generate_2d_concentric_fill'):
                        print("错误: generate_2d_concentric_fill 方法未找到!")
                        continue
                    fill_points_2d, connection_types, offset_polygon_shapely = self.generate_2d_concentric_fill(
                        current_contour_2d_points, adjusted_2d_row_spacing, 
                        offset_distance=adjusted_2d_offset_for_fill, 
                        max_segment_length=max_segment_length,
                        inner_contours_list=inner_contours_for_this_external
                    )
                else:
                    print(f"错误: 未知的2D填充模式 '{projection_fill_pattern}'.")
                    continue

                if fill_points_2d is not None and len(fill_points_2d) > 0:
                    # 保存2D填充数据用于后续可视化，而不是立即显示
                    all_2d_fill_data.append((current_contour_2d_points, offset_polygon_shapely, fill_points_2d, connection_types, adjusted_2d_row_spacing))
                else:
                    print(f"轮廓 {ex_contour_data['id']} 未能生成2D填充路径。")
                    continue 
                
                # Ensure project_2d_points_to_3d_surface and build_final_3d_paths exist
                if not hasattr(self, 'project_2d_points_to_3d_surface') or not hasattr(self, 'build_final_3d_paths'):
                    print("错误: 缺少 project_2d_points_to_3d_surface 或 build_final_3d_paths 方法!")
                    continue

                projected_3d_pts, projected_3d_normals, success_mask = self.project_2d_points_to_3d_surface(fill_points_2d)
                if projected_3d_pts is None or len(projected_3d_pts) == 0:
                    print(f"轮廓 {ex_contour_data['id']} 未能将任何2D填充点投影到3D曲面。")
                    continue 

                # 保存投影连接数据
                all_projection_connections.append((fill_points_2d, projected_3d_pts, success_mask))

                current_contour_fill_id_start = base_fill_segment_id_start - (contour_index * id_step_per_contour_fill)
                fill_segments_for_this_contour = self.build_final_3d_paths(
                    projected_3d_pts, projected_3d_normals, success_mask, 
                    fill_points_2d, connection_types, 
                    boundary_paths_data=None, 
                    projected_segment_id_start=current_contour_fill_id_start 
                )
                
                if fill_segments_for_this_contour:
                    final_paths_for_all_contours.extend(fill_segments_for_this_contour)

                    # 为投影策略计算和收集间距数据
                    # 提取当前轮廓的填充条带 (is_boundary=False)
                    current_contour_fill_strips = []
                    for seg_points, seg_normals, is_b, seg_id in fill_segments_for_this_contour:
                        if not is_b: # 只处理填充条带
                            current_contour_fill_strips.append({
                                'points': seg_points, 
                                'normals': seg_normals,
                                'id': seg_id # 使用段ID作为"offset_val"的代理
                            })
                    
                    if len(current_contour_fill_strips) > 1:
                        print(f"  (投影策略) 为轮廓 {ex_contour_data['id']} 的 {len(current_contour_fill_strips)} 个填充条带计算3D间距...")
                        scan_axis_for_projection = 1 - self.axis_index # 与direct_offset的scan_axis定义一致

                        for i in range(len(current_contour_fill_strips) - 1):
                            strips_layer1_data = [current_contour_fill_strips[i]] # _calculate_actual_3d_spacing_between_strip_sets期望列表
                            strips_layer2_data = [current_contour_fill_strips[i+1]]

                            # 确保条带数据包含必要的键 (points, normals, id)
                            # 这里的 'id' 被用作 offset_val 的替代品
                            offset_val_1 = abs(current_contour_fill_strips[i]['id']) 
                            offset_val_2 = abs(current_contour_fill_strips[i+1]['id'])

                            spacing_stats = self._calculate_actual_3d_spacing_between_strip_sets(
                                strips_layer1_data, strips_layer2_data, 
                                scan_axis=scan_axis_for_projection, # 使用与direct_offset一致的扫描轴定义
                                num_samples_on_strip1=10 # 为投影策略的间距分析设置固定采样数
                            )

                            if spacing_stats and spacing_stats['samples'] > 0:
                                error_val = spacing_stats['mean'] - target_3d_row_spacing # 使用目标行距计算误差
                                spacing_analysis_data_projection.append({
                                    'Offset1_mm': offset_val_1, # 使用ID的绝对值
                                    'Offset2_mm': offset_val_2, # 使用ID的绝对值
                                    'ProjectedStep_mm': adjusted_2d_row_spacing, # 使用2D填充时的行距
                                    'Target3DSpacing_mm': target_3d_row_spacing,
                                    'ActualAvg3DSpacing_mm': spacing_stats['mean'],
                                    'Error_mm': error_val,
                                    'Min3DSpacing_mm': spacing_stats['min'],
                                    'Max3DSpacing_mm': spacing_stats['max'],
                                    'Median3DSpacing_mm': spacing_stats['median'],
                                    'StdDev3DSpacing_mm': spacing_stats['std'],
                                    'NumSamples': spacing_stats['samples']
                                })
                            else:
                                # 如果无法计算间距，仍添加一条记录，但值为 NaN
                                spacing_analysis_data_projection.append({
                                    'Offset1_mm': offset_val_1,
                                    'Offset2_mm': offset_val_2,
                                    'ProjectedStep_mm': adjusted_2d_row_spacing,
                                    'Target3DSpacing_mm': target_3d_row_spacing,
                                    'ActualAvg3DSpacing_mm': float('nan'),
                                    'Error_mm': float('nan'),
                                    'Min3DSpacing_mm': float('nan'),
                                    'Max3DSpacing_mm': float('nan'),
                                    'Median3DSpacing_mm': float('nan'),
                                    'StdDev3DSpacing_mm': float('nan'),
                                    'NumSamples': 0
                                })
                        print(f"    为轮廓 {ex_contour_data['id']} 添加了 {len(current_contour_fill_strips) -1} 条间距记录。")


            # 显示集成的可视化（3D路径 + 2D填充数据）
            if all_2d_fill_data and all_projection_connections:
                print(f"\n--- 显示投影策略的集成可视化（3D路径 + 2D填充）---")
                
                # 合并所有2D填充数据用于可视化（取第一个轮廓作为主要显示）
                main_2d_data = all_2d_fill_data[0][:4]  # 取前4个元素：(contour_2d, offset_polygon, fill_points_2d, connection_types)
                
                # 合并所有投影连接数据
                all_fill_points_2d = []
                all_projected_3d_pts = []
                all_success_masks = []
                
                for points_2d, points_3d, success_mask in all_projection_connections:
                    all_fill_points_2d.append(points_2d)
                    all_projected_3d_pts.append(points_3d)
                    all_success_masks.append(success_mask)
                
                if all_fill_points_2d:
                    # 合并所有数据
                    combined_fill_points_2d = np.vstack(all_fill_points_2d)
                    combined_projected_3d_pts = np.vstack(all_projected_3d_pts)
                    combined_success_mask = np.concatenate(all_success_masks)
                    
                    combined_projection_connections = (combined_fill_points_2d, combined_projected_3d_pts, combined_success_mask)
                    
                    # 调用新的可视化方法
                    self.visualize_paths(final_paths_for_all_contours, 
                                       show_normals=False, 
                                       normal_scale=0.5, 
                                       normal_hop_distance=None, 
                                       clearance_above_model_max_viz=None,
                                       show_2d_fill=True,
                                       fill_2d_data=main_2d_data, # 使用第一个轮廓的2D数据作为主要显示
                                       projection_connections=combined_projection_connections) # 使用合并的连接数据
            
            print(f"\n投影切片流程完成，共生成 {len(final_paths_for_all_contours)} 个路径段。")
            return final_paths_for_all_contours, spacing_analysis_data_projection # 返回路径列表和投影策略的间距数据
        else:
            print(f"错误：当前版本只支持 'direct_offset' 或 'projection' 策略，不支持 '{strategy}'。")
            return [], [] # 返回空列表以匹配期望的返回类型

    def _get_3d_strips_for_single_offset_val_optimized(self, offset_val, offset_dir_axis, 
                                           boundary_shapely_linestring, boundary_kdtree, 
                                           inner_contours_shapely_list, 
                                           proximity_threshold, max_segment_length):
        """
        优化版本的3D条带生成方法
        ENHANCED: 添加了早期终止、质量检查和性能优化
        """
        generated_3d_strips = []
        
        # 1. 优化的3D切片，带异常处理和早期验证
        plane_normal = np.zeros(3)
        plane_normal[offset_dir_axis] = 1.0
        plane_origin = self.mesh.centroid.copy()
        plane_origin[offset_dir_axis] = offset_val

        # 早期边界检查：如果偏移值明显超出网格范围，直接返回
        mesh_bounds = self.mesh.bounds
        if (offset_val < mesh_bounds[0][offset_dir_axis] - 1.0 or 
            offset_val > mesh_bounds[1][offset_dir_axis] + 1.0):
            return []

        try:
            path3d_slice_object = self.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
        except Exception as e:
            # 静默处理，避免过多日志输出
            return []

        initial_3d_polylines = []
        if path3d_slice_object is not None:
            if hasattr(path3d_slice_object, 'discrete') and path3d_slice_object.discrete:
                initial_3d_polylines.extend(path3d_slice_object.discrete)
            elif hasattr(path3d_slice_object, 'vertices') and path3d_slice_object.vertices is not None and len(path3d_slice_object.vertices) >= 2:
                initial_3d_polylines.append(path3d_slice_object.vertices)

        if not initial_3d_polylines:
            return []

        # 2. 优化的多线条处理，带质量过滤
        for polyline_3d_initial_pts in initial_3d_polylines:
            if len(polyline_3d_initial_pts) < 2: 
                continue

            # 质量检查：过滤过短或退化的线条
            polyline_length = self._calculate_polyline_length(polyline_3d_initial_pts)
            if polyline_length < max_segment_length * 0.5:  # 过短的线条
                continue

            # 智能插值：根据线条复杂度调整插值密度
            adaptive_max_segment = max_segment_length
            if polyline_length > 20.0:  # 长线条使用稍大的段长
                adaptive_max_segment *= 1.2
            elif polyline_length < 2.0:  # 短线条使用更小的段长
                adaptive_max_segment *= 0.8

            # 插值处理
            interpolated_3d_path = self.interpolate_path_points(polyline_3d_initial_pts, adaptive_max_segment)
            
            if len(interpolated_3d_path) >= self.min_points_req:
                generated_3d_strips.append(interpolated_3d_path)

        return generated_3d_strips

    def _get_3d_strips_for_single_offset_val(self, offset_val, offset_dir_axis, 
                                           boundary_shapely_linestring, boundary_kdtree, 
                                           inner_contours_shapely_list, 
                                           proximity_threshold, max_segment_length):
        """
        Generates 3D path strips for a single given offset value.
        MODIFIED: Uses direct 3D slicing instead of 2D intersection for initial strip generation.
        REMOVED: Logic for splitting strips based on `inner_contours_shapely_list` (buffered holes) 
                 as this is now handled later by `_segment_strip_by_3d_intersections`.

        Args:
            offset_val (float): The offset value (e.g., Y-coordinate) for which to generate paths.
            offset_dir_axis (int): The axis of offsetting (0 for X, 1 for Y).
            boundary_shapely_linestring (shapely.LineString): Main boundary (not directly used here anymore).
            boundary_kdtree (scipy.spatial.KDTree): KDTree for boundary (not directly used here anymore).
            inner_contours_shapely_list (list): Buffered 2D hole polygons (NO LONGER USED for splitting here).
            proximity_threshold (float): Min distance (NO LONGER USED for splitting here).
            max_segment_length (float): Maximum segment length for interpolating 3D paths.

        Returns:
            list: A list of numpy arrays, where each array is a 3D path strip.
        """
        generated_3d_strips = []
        
        # 1. Perform direct 3D slicing using self.mesh.section()
        plane_normal = np.zeros(3)
        plane_normal[offset_dir_axis] = 1.0
        plane_origin = self.mesh.centroid.copy() # Use mesh centroid as a base for plane origin
        plane_origin[offset_dir_axis] = offset_val

        try:
            path3d_slice_object = self.mesh.section(plane_origin=plane_origin, plane_normal=plane_normal)
        except Exception as e:
            print(f"    WARNING (_get_3d_strips): Error during self.mesh.section() at offset {offset_val:.3f}: {e}")
            return [] # Return empty if slicing fails

        initial_3d_polylines = []
        if path3d_slice_object is not None:
            if path3d_slice_object.discrete: # section can return a list of polylines
                initial_3d_polylines.extend(path3d_slice_object.discrete)
            elif path3d_slice_object.vertices is not None and len(path3d_slice_object.vertices) >= 2:
                initial_3d_polylines.append(path3d_slice_object.vertices)

        if not initial_3d_polylines:
            return []

        # Process each 3D polyline obtained from the slice
        for polyline_3d_initial_pts in initial_3d_polylines:
            if len(polyline_3d_initial_pts) < 2: 
                continue

            # Interpolate the raw 3D path strip
            # Normals will be calculated later if needed, or by the consuming function.
            interpolated_3d_path = self.interpolate_path_points(polyline_3d_initial_pts, max_segment_length)
            
            if len(interpolated_3d_path) >= self.min_points_req:
                generated_3d_strips.append(interpolated_3d_path)
        
        # Debugging print (can be removed or kept as needed)
        if offset_dir_axis == 0: # Example for X-axis offset debugging
            if generated_3d_strips:
                print(f"    DEBUG _get_3d_strips: Generated {len(generated_3d_strips)} raw 3D strips for offset_val(X)={offset_val:.4f} (hole avoidance removed from this stage).")
            else:
                print(f"    DEBUG _get_3d_strips: No raw 3D strips generated for offset_val(X)={offset_val:.4f} (hole avoidance removed from this stage).")

        return generated_3d_strips

    def save_spacing_analysis_to_file(self, spacing_data, output_csv_file):
        """
        将间距分析数据保存到CSV文件。

        参数:
        spacing_data: 包含间距分析字典的列表。
        output_csv_file: 输出CSV文件的路径。
        """
        if not spacing_data:
            print(f"警告: 没有间距分析数据可保存到 {output_csv_file}。")
            return

        fieldnames = [
            'Offset1_mm', 'Offset2_mm', 'ProjectedStep_mm', 
            'Target3DSpacing_mm', 'ActualAvg3DSpacing_mm', 'Error_mm', 
            'Min3DSpacing_mm', 'Max3DSpacing_mm', 'Median3DSpacing_mm', 
            'StdDev3DSpacing_mm', 'NumSamples'
        ]

        try:
            with open(output_csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for row_dict in spacing_data:
                    # 格式化浮点数以获得一致的输出
                    formatted_row = {k: (f"{v:.4f}" if isinstance(v, float) and not np.isnan(v) else v) for k, v in row_dict.items()}
                    writer.writerow(formatted_row)
            print(f"间距分析数据已成功保存到: {output_csv_file}")
        except IOError as e:
            print(f"错误: 无法写入间距分析文件 {output_csv_file}: {e}")
        except Exception as e:
            print(f"错误: 保存间距分析数据时发生未知错误: {e}")

    def generate_adaptive_slice_positions_iterative(self, start_offset, end_offset, 
                                                    target_3d_spacing_objective, 
                                                    offset_dir_axis, 
                                                    # Args for _get_3d_strips_for_single_offset_val
                                                    boundary_shapely_linestring, 
                                                    inner_contours_shapely_list, 
                                                    proximity_threshold, 
                                                    max_segment_length,
                                                    # Iteration control params
                                                    min_delta_y_factor=0.05, 
                                                    max_delta_y_factor=2.0, # Added max factor
                                                    tolerance_abs=0.1, # Absolute tolerance in mm
                                                    iter_max_iterations_per_step=15, # Added missing parameter
                                                    iter_num_samples_for_spacing_calc=7 # Renamed for consistency
                                                    # Removed use_even_odd_rule
                                                    ):
        """
        Generates adaptive slice positions using an iterative feedback loop to achieve 
        a target 3D spacing between path strips.

        Args:
            start_offset (float): Starting offset value.
            end_offset (float): Ending offset value.
            target_3d_spacing_objective (float): The desired 3D distance between path strips.
            offset_dir_axis (int): The axis of offsetting (0 for X, 1 for Y).
            boundary_shapely_linestring (shapely.LineString): Main boundary for path generation.
            inner_contours_shapely_list (list): List of Shapely Polygons for inner holes (buffered).
            proximity_threshold (float): Min distance from boundaries/holes for path generation.
            max_segment_length (float): Max segment length for interpolating 3D paths.
            min_delta_y_factor (float): Min allowed step (delta_y) as a factor of target_3d_spacing_objective.
            max_delta_y_factor (float): Max allowed step (delta_y) as a factor of target_3d_spacing_objective.
            tolerance_abs (float): Absolute tolerance (in mm) for matching the target 3D spacing.
            iter_max_iterations_per_step (int): Max iterations to find delta_y for each step.
            iter_num_samples_for_spacing_calc (int): Samples for _calculate_actual_3d_spacing_between_strip_sets.
        """
        # 使用 self.damping_factor_iterative_spacing
        current_damping_factor = self.damping_factor_iterative_spacing

        if start_offset >= end_offset or target_3d_spacing_objective <= 0:
            return []

        # 增强: 记录开始时间用于性能统计
        start_time = time.time()

        print(f"\n  基于迭代反馈生成自适应切片位置:")
        print(f"    目标3D间距: {target_3d_spacing_objective:.3f}mm")
        print(f"    偏置轴: {'X' if offset_dir_axis == 0 else 'Y'}")
        print(f"    迭代参数: MinΔYFactor={min_delta_y_factor}, MaxΔYFactor={max_delta_y_factor}, AbsTolerance={tolerance_abs}mm, MaxIterPerStep={iter_max_iterations_per_step}")
        # Removed print(f"    使用奇偶规则: {'是' if use_even_odd_rule else '否'}")

        slice_positions = [start_offset] # Start with the initial offset
        current_pos = start_offset
        total_iterations_overall = 0
        max_overall_iterations = 2000 # Safety break for the entire process

        # Get initial strips for the starting position
        # Replaced placeholder _get_raw_or_simplified_strips_at_offset with _get_3d_strips_for_single_offset_val
        # boundary_kdtree argument is not used by _get_3d_strips_for_single_offset_val, so passing None is fine.
        strips_at_current_pos = self._get_3d_strips_for_single_offset_val(
                current_pos, offset_dir_axis, 
                boundary_shapely_linestring, None, # boundary_kdtree is None
                inner_contours_shapely_list, # These are buffered hole polygons
                proximity_threshold, max_segment_length
            )


        if not strips_at_current_pos:
            print(f"    警告: 在起始位置 {current_pos:.3f} 未能生成路径条带。无法继续。")
            return slice_positions # Return just the start if no strips generated

        min_delta_y_abs = target_3d_spacing_objective * min_delta_y_factor
        max_delta_y_abs = target_3d_spacing_objective * max_delta_y_factor
        scan_axis = 1 - offset_dir_axis

        # 增强: 高级自适应算法参数
        delta_y_history = []  # 步长历史，用于智能预测
        spacing_error_history = []  # 间距误差历史
        convergence_quality_history = []  # 收敛质量历史
        max_history_length = 5  # 增加历史长度以提高预测准确性
        convergence_count = 0   # 连续收敛计数器

        # 增强: 智能预测缓存系统
        position_cache = {}  # 缓存最近计算的位置和条带
        cache_tolerance = 0.012  # 收紧容差以提高精度
        cache_hit_count = 0  # 缓存命中计数

        # 增强: 性能和质量平衡
        total_positions_needed = (end_offset - start_offset) / target_3d_spacing_objective
        progress_interval = max(1, int(total_positions_needed / 10))  # 适度减少输出频率
        position_counter = 0

        # 增强: 自适应学习参数 - 动态调整
        base_learning_rate = 0.4  # 提高基础学习率
        learning_rate = base_learning_rate
        momentum = 0.15       # 增加动量以减少振荡
        adaptive_factor = 1.0  # 自适应调整因子

        # 增强: 智能初始步长预测系统
        surface_complexity_factor = self._estimate_surface_complexity()
        initial_step_predictor = target_3d_spacing_objective * (0.85 - 0.1 * surface_complexity_factor)
        step_adaptation_factor = 1.0 + 0.2 * surface_complexity_factor  # 基于表面复杂度调整

        # 增强: 收敛质量控制
        quality_threshold = 0.95  # 质量阈值
        consecutive_good_predictions = 0  # 连续良好预测计数

        while current_pos < end_offset and total_iterations_overall < max_overall_iterations:
            total_iterations_overall += 1
            position_counter += 1
            best_delta_y = None
            best_measured_spacing = float('inf')
            best_error = float('inf')
            strips_at_best_next_pos = None
            final_next_pos_for_this_step = None
            
            # 性能优化：减少日志输出
            show_detailed_output = (position_counter % progress_interval == 0)
            
            if show_detailed_output:
                progress_pct = ((current_pos - start_offset) / (end_offset - start_offset)) * 100
                print(f"  进度 {progress_pct:.1f}% - 位置 {current_pos:.3f}mm")

            # 革命性方法：使用直接计算代替迭代收敛
            # 这是解决21.2%收敛率问题的核心改进

            # 计算表面复杂度因子
            surface_complexity = surface_complexity_factor

            # 使用革命性直接间距计算器
            optimal_delta = self._revolutionary_direct_spacing_calculator(
                current_pos, target_3d_spacing_objective, surface_complexity
            )

            # 基于历史数据的快速调整
            history_data = []
            if len(delta_y_history) > 0 and len(spacing_error_history) > 0:
                for i in range(min(len(delta_y_history), len(spacing_error_history))):
                    history_data.append({
                        'delta': delta_y_history[-(i+1)],
                        'measured': target_3d_spacing_objective - spacing_error_history[-(i+1)],
                        'target': target_3d_spacing_objective
                    })

            # 快速收敛预测
            predicted_delta = self._fast_convergence_predictor(
                optimal_delta, history_data
            )

            # 应用边界约束
            predicted_delta = max(min_delta_y_abs, min(predicted_delta, max_delta_y_abs))

            # 基于表面复杂度的最终调整
            predicted_delta *= step_adaptation_factor

            delta_y_trial = predicted_delta
            
            # 确保delta_y_trial是有效数值
            if np.isnan(delta_y_trial) or np.isinf(delta_y_trial):
                delta_y_trial = target_3d_spacing_objective
            
            delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)

            # 只在详细输出时显示
            if show_detailed_output:
                print(f"    寻找下一位置，初始ΔY={delta_y_trial:.4f}mm")
            
            # 简化的结果记录
            iteration_results = []
            
            for iter_count in range(iter_max_iterations_per_step):
                next_pos_trial = current_pos + delta_y_trial
                if next_pos_trial >= end_offset + min_delta_y_abs: # Don't overshoot too much
                    print(f"    尝试ΔY={delta_y_trial:.4f} (下一位置 {next_pos_trial:.3f}) 超出结束点 {end_offset:.3f}，结束此步迭代。")
                    # If this is the first trial and it overshoots, we might not find a best_delta_y
                    if best_delta_y is None and next_pos_trial > current_pos + min_delta_y_abs / 2 : # Ensure it's a valid step
                        # Potentially add end_offset if it's a valid step, handled later
                        pass 
                    break 

                strips_at_next_pos_trial = None # Initialize before assignment

                # 智能缓存检查：避免重复计算相似位置
                strips_at_next_pos_trial = None
                cache_key = None
                
                # 检查缓存
                for cached_pos, cached_strips in position_cache.items():
                    if abs(cached_pos - next_pos_trial) < cache_tolerance:
                        strips_at_next_pos_trial = cached_strips
                        cache_hit_count += 1  # 记录缓存命中
                        if show_detailed_output:
                            print(f"    缓存命中: 位置 {next_pos_trial:.3f} -> {cached_pos:.3f}")
                        break
                
                if strips_at_next_pos_trial is None:
                    # 缓存未命中，需要计算新的条带
                    strips_at_next_pos_trial = self._get_3d_strips_for_single_offset_val_optimized(
                        next_pos_trial, offset_dir_axis,
                        boundary_shapely_linestring, None, # boundary_kdtree is None
                        inner_contours_shapely_list, # These are buffered hole polygons
                        proximity_threshold, max_segment_length
                    )
                    
                    # 更新缓存，保持缓存大小合理
                    if len(position_cache) >= 10:  # 限制缓存大小
                        # 移除最远的缓存条目
                        furthest_key = max(position_cache.keys(), key=lambda k: abs(k - next_pos_trial))
                        del position_cache[furthest_key]
                    
                    position_cache[next_pos_trial] = strips_at_next_pos_trial

                if not strips_at_next_pos_trial:
                    print(f"    尝试ΔY={delta_y_trial:.4f} (下一位置 {next_pos_trial:.3f}): 未能生成路径条带。尝试减小ΔY。")
                    delta_y_trial *= 0.75 # Reduce delta_y and try again
                    delta_y_trial = max(delta_y_trial, min_delta_y_abs) # Ensure it doesn't go below min
                    if iter_count == iter_max_iterations_per_step -1 and best_delta_y is None:
                         print(f"    在最后一次尝试减小ΔY后仍无法生成条带。")
                    continue

                spacing_stats = self._calculate_actual_3d_spacing_between_strip_sets(
                    strips_at_current_pos, strips_at_next_pos_trial, 
                    scan_axis, num_samples_on_strip1=iter_num_samples_for_spacing_calc
                )
                
                # 使用中位数而非平均值作为主要反馈，更鲁棒
                measured_dist_iter = spacing_stats['median']
                if np.isnan(measured_dist_iter):
                    # 如果中位数无效，尝试使用平均值
                    measured_dist_iter = spacing_stats['mean']

                # 增强: 验证和修正间距测量结果，使用70%阈值
                if not np.isnan(measured_dist_iter):
                    corrected_spacing, was_corrected = self._validate_and_correct_spacing(
                        measured_dist_iter, target_3d_spacing_objective, tolerance_factor=0.07
                    )
                    if was_corrected:
                        measured_dist_iter = corrected_spacing
                        # 记录修正统计
                        self._performance_stats['spacing_warnings'] += 1

                        # 早期终止逻辑：防止过多间距警告
                        if self._performance_stats['spacing_warnings'] >= 3:
                            print(f"    ⚠️  达到间距警告限制(3)，启用保守模式")
                            # 切换到更保守的参数
                            old_tolerance = tolerance_abs
                            old_max_iter = iter_max_iterations_per_step
                            tolerance_abs = min(tolerance_abs * 1.5, 0.05)  # 放宽容差
                            iter_max_iterations_per_step = max(3, iter_max_iterations_per_step // 2)  # 减少迭代
                            print(f"    📊 保守模式参数: 容差 {old_tolerance:.4f}→{tolerance_abs:.4f}mm, 最大迭代 {old_max_iter}→{iter_max_iterations_per_step}")

                            # 防止无限警告：如果已经在保守模式下仍有警告，则跳过后续严格检查
                            if self._performance_stats['spacing_warnings'] >= 5:
                                print(f"    🛑 保守模式下仍有过多警告，跳过严格验证以确保完成")
                                # 可以选择直接接受当前间距或使用目标间距
                                measured_dist_iter = target_3d_spacing_objective

                    # 更新质量监控指标
                    self._update_quality_metrics(measured_dist_iter, target_3d_spacing_objective)
                
                if np.isnan(measured_dist_iter) or spacing_stats['samples'] == 0:
                    if show_detailed_output:
                        print(f"    ΔY={delta_y_trial:.4f}: 无法计算间距 (样本数: {spacing_stats['samples']})")
                    # 智能调整：基于历史数据
                    if len(iteration_results) > 0:
                        last_good_delta = iteration_results[-1]['delta_y']
                        delta_y_trial = (delta_y_trial + last_good_delta) / 2
                    else:
                        # 使用保守的调整策略
                        delta_y_trial *= 0.9 if best_measured_spacing > target_3d_spacing_objective else 1.1
                    delta_y_trial = np.clip(delta_y_trial, min_delta_y_abs, max_delta_y_abs)
                    continue

                error_abs = abs(measured_dist_iter - target_3d_spacing_objective)
                error_signed = measured_dist_iter - target_3d_spacing_objective  # 带符号的误差
                
                # 记录本次尝试的结果
                iteration_results.append({
                    'delta_y': delta_y_trial,
                    'measured_spacing': measured_dist_iter,
                    'error_abs': error_abs,
                    'error_signed': error_signed
                })
                
                # 只在详细输出或首次迭代时显示
                if show_detailed_output or iter_count == 0:
                    print(f"    迭代 {iter_count+1}: ΔY={delta_y_trial:.4f} -> 间距={measured_dist_iter:.4f}mm (误差={error_signed:+.4f}mm)")

                # 优化: 使用更严格的收敛标准，提高间距质量
                effective_tolerance = tolerance_abs * 0.8  # 收紧容差20%，提高精度

                if error_abs < effective_tolerance:
                    best_delta_y = delta_y_trial
                    best_measured_spacing = measured_dist_iter
                    best_error = error_abs
                    strips_at_best_next_pos = strips_at_next_pos_trial
                    final_next_pos_for_this_step = next_pos_trial
                    convergence_count += 1
                    # 记录收敛质量
                    convergence_quality = 1.0 - (error_abs / effective_tolerance)
                    convergence_quality_history.append(convergence_quality)
                    consecutive_good_predictions += 1

                    if show_detailed_output:
                        print(f"      收敛! ΔY={best_delta_y:.4f} (质量={convergence_quality:.3f}, 误差={error_abs:.4f}mm)")
                    break # Found a good enough delta_y
                
                # Update if this is the best one found so far
                if error_abs < best_error:
                    best_delta_y = delta_y_trial
                    best_measured_spacing = measured_dist_iter
                    best_error = error_abs
                    strips_at_best_next_pos = strips_at_next_pos_trial
                    final_next_pos_for_this_step = next_pos_trial

                # 记录误差历史用于学习
                spacing_error_history.append(error_signed)
                if len(spacing_error_history) > max_history_length:
                    spacing_error_history.pop(0)
                
                # 恢复原始简化的自适应调整算法（替代PID）
                # 基于误差大小和方向进行调整
                error_ratio = error_signed / target_3d_spacing_objective

                if abs(error_ratio) > 0.3:  # 大误差，大步调整
                    adjustment_factor = 0.8 if error_signed > 0 else 1.25
                elif abs(error_ratio) > 0.1:  # 中等误差，中步调整
                    adjustment_factor = 0.9 if error_signed > 0 else 1.1
                else:  # 小误差，小步调整
                    adjustment_factor = 0.95 if error_signed > 0 else 1.05

                new_delta_y = delta_y_trial * adjustment_factor

                # 防振荡：如果连续调整方向相反，使用二分法
                if len(iteration_results) >= 2:
                    prev_error = iteration_results[-2]['error_signed']
                    if (prev_error > 0) != (error_signed > 0):
                        # 方向相反，使用二分法
                        prev_delta = iteration_results[-2]['delta_y']
                        new_delta_y = (delta_y_trial + prev_delta) / 2

                # 应用约束
                delta_y_trial = np.clip(new_delta_y, min_delta_y_abs, max_delta_y_abs)
            
            # After iteration loop for one step
            if best_delta_y is not None:
                # 只在详细输出时显示完整信息
                if show_detailed_output:
                    print(f"    选定: ΔY={best_delta_y:.4f}mm, 下一位置: {final_next_pos_for_this_step:.3f}mm (误差: {best_error:.4f}mm)")
                
                # 记录成功的步长到历史
                delta_y_history.append(best_delta_y)
                if len(delta_y_history) > max_history_length:
                    delta_y_history.pop(0)
                    
                current_pos = final_next_pos_for_this_step
                slice_positions.append(current_pos)
                strips_at_current_pos = strips_at_best_next_pos
                
                # 优化的早期停止条件：保持严格的质量标准
                if convergence_count >= 8:  # 增加收敛次数要求
                    # 仅在连续收敛质量很高时才适度放宽容差
                    avg_convergence_quality = np.mean(convergence_quality_history) if convergence_quality_history else 0.0
                    if avg_convergence_quality > 0.8:
                        tolerance_abs = min(tolerance_abs * 1.1, target_3d_spacing_objective * 0.08)  # 更保守的放宽
                
                if not strips_at_current_pos:
                    print(f"    警告: 无法生成下一位置的路径条带")
                    break
            else:
                # 未找到合适的delta_y
                if show_detailed_output:
                    print(f"    未找到理想ΔY")
                if current_pos < end_offset and (end_offset - current_pos) >= min_delta_y_abs / 2:
                    slice_positions.append(end_offset)
                break

        # Final check on the last position - 更智能的边界处理
        if slice_positions and slice_positions[-1] < end_offset:
            remaining_distance = end_offset - slice_positions[-1]
            
            # 如果剩余距离很小，直接调整最后一个位置
            if remaining_distance < min_delta_y_abs / 2 and remaining_distance > 1e-3:
                print(f"    微调最后一个切片位置从 {slice_positions[-1]:.3f} 到 {end_offset:.3f}")
                slice_positions[-1] = end_offset
            # 如果剩余距离适中，添加最后一个切片
            elif remaining_distance >= min_delta_y_abs:
                print(f"    添加最后一个切片位置: {end_offset:.3f}")
                slice_positions.append(end_offset)
            # 如果剩余距离太小，保持现状
            else:
                print(f"    剩余距离 {remaining_distance:.3f}mm 太小，不添加额外切片")
                
        elif slice_positions and slice_positions[-1] > end_offset: # If overshot, remove last if it is not the only one after start
            if len(slice_positions) > 1 and slice_positions[-1] > end_offset + tolerance_abs : # only remove if significantly over
                print(f"    最后一个计算的切片位置 {slice_positions[-1]:.3f} 超出了结束点 {end_offset:.3f}，正在移除。")
                slice_positions.pop()
                # Optionally, add end_offset if the new last position is far from it.
                if slice_positions and (end_offset - slice_positions[-1]) >= min_delta_y_abs:
                    slice_positions.append(end_offset)
            elif len(slice_positions) == 1 and slice_positions[0] > end_offset: # start_offset itself is beyond end_offset
                print(f"    起始切片 {slice_positions[0]:.3f} 已超出结束点 {end_offset:.3f}，列表将为空。")
                return []
        
        # 优化的质量验证和统计
        print(f"  自适应切片完成: {len(slice_positions)} 个位置 (总迭代: {total_iterations_overall})")
        if len(slice_positions) > 1:
            # 计算关键统计指标
            actual_spacings = np.diff(slice_positions)
            avg_spacing_actual = np.mean(actual_spacings)
            rms_error = np.sqrt(np.mean((actual_spacings - target_3d_spacing_objective)**2))

            # 质量评估 - 使用更严格的评分标准
            quality_score = 100.0 * np.exp(-2.0 * (rms_error / target_3d_spacing_objective))

            print(f"    间距质量: 平均={avg_spacing_actual:.4f}mm, RMS误差={rms_error:.4f}mm")
            print(f"    质量评分: {quality_score:.1f}% (收敛次数: {convergence_count})")

            # 增强: 更新性能统计
            total_duration = time.time() - start_time
            avg_convergence_quality = np.mean(convergence_quality_history) if convergence_quality_history else 0.0
            convergence_rate = convergence_count / total_iterations_overall if total_iterations_overall > 0 else 0.0

            # 正确计算缓存命中率
            total_cache_requests = getattr(self, '_cache_hits', 0) + getattr(self, '_cache_misses', 0)
            cache_hit_rate = (getattr(self, '_cache_hits', 0) / total_cache_requests) if total_cache_requests > 0 else 0.0

            # 更新性能统计
            self._performance_stats.update({
                'convergence_rate': convergence_rate,
                'cache_hit_rate': cache_hit_rate,
                'avg_convergence_quality': avg_convergence_quality,
                'total_duration': total_duration,
                'positions_generated': len(slice_positions),
                'total_iterations': total_iterations_overall
            })

            # 增强: 打印详细性能报告
            print(f"    === 增强算法性能报告 ===")
            print(f"    总耗时: {total_duration:.4f} 秒")
            print(f"    收敛率: {convergence_rate:.1%} ({convergence_count}/{total_iterations_overall})")
            print(f"    平均收敛质量: {avg_convergence_quality:.3f}")
            print(f"    缓存命中率: {cache_hit_rate:.1%}")

            if len(slice_positions) > 0:
                avg_iterations_per_position = total_iterations_overall / len(slice_positions)
                positions_per_second = len(slice_positions) / total_duration if total_duration > 0 else 0
                print(f"    平均每位置迭代次数: {avg_iterations_per_position:.2f}")
                print(f"    位置生成速度: {positions_per_second:.1f} 位置/秒")

            # 记录收敛历史用于后续分析
            self._convergence_history.extend(convergence_quality_history)

            # 计算并报告最终质量评分
            final_quality_score = self._calculate_current_quality_score()
            print(f"    === 最终性能评估 ===")
            print(f"    综合质量评分: {final_quality_score:.1f}% (目标: >85%)")

            # 检查是否达到所有性能目标
            performance_targets_met = {
                'target_achievement_rate': self._performance_stats.get('target_achievement_rate', 0) >= 95,
                'rms_error_acceptable': self._performance_stats.get('rms_error_percentage', 100) < 15,
                'spacing_warnings_low': self._performance_stats.get('spacing_warnings', 999) < 3,
                'quality_score_high': final_quality_score > 85,
                'cache_hit_rate_good': self._performance_stats.get('cache_hit_rate', 0) * 100 > 20,
                'path_rejection_low': self._performance_stats.get('path_rejection_rate', 100) < 50
            }

            targets_met_count = sum(performance_targets_met.values())
            total_targets = len(performance_targets_met)

            print(f"    性能目标达成: {targets_met_count}/{total_targets} ({targets_met_count/total_targets*100:.1f}%)")
            for target, met in performance_targets_met.items():
                status = "✓" if met else "✗"
                print(f"      {target}: {status}")

            if targets_met_count == total_targets:
                print(f"    🎉 所有性能目标已达成！")
            else:
                print(f"    ⚠️  需要进一步优化以达成所有目标")

        elif len(slice_positions) == 1:
             print(f"    单一位置: {slice_positions[0]:.3f}mm")
        else:
            print(f"    无有效位置")

        return slice_positions

    
    def _is_3d_segment_inside_material_batch(self, points_3d_array):
        """
        批量判断3D点是否在材料内部
        
        Args:
            points_3d_array (numpy.ndarray): 3D点数组，形状为 (n, 3)
            
        Returns:
            numpy.ndarray: 布尔数组，True表示对应点在材料内部
        """
        try:
            # 使用trimesh的批量contains方法
            inside_mask = self.mesh.contains(points_3d_array)
            return inside_mask
        except Exception as e:
            print(f"警告: 批量3D材料内部判断失败: {e}")
            return np.zeros(len(points_3d_array), dtype=bool)

    def _segment_strip_by_3d_intersections(self, strip_points_input, strip_normals_input, all_3d_boundary_linestrings):
        """
        Segments a 3D path strip based on its intersections with 3D boundary polylines.
        Uses an even-odd rule to identify fill segments.

        Args:
            strip_points_input (np.ndarray): The 3D points of the path strip.
            strip_normals_input (np.ndarray): The corresponding 3D normals for the strip points.
            all_3d_boundary_linestrings (list): List of Shapely LineString objects representing 3D boundaries.

        Returns:
            list: A list of dictionaries, where each dictionary is a fill segment:
                  {'points': np.ndarray, 'normals': np.ndarray}
        """
        fill_segments_found = []
        if len(strip_points_input) < self.min_points_req:
            return fill_segments_found

        # Helper to convert 3D points to Shapely LineString (can be defined locally or be a class method)
        def to_shapely_linestring_3d_local(points):
            if len(points) < 2:
                return None
            return LineString(points)

        current_strip_shapely_3d = to_shapely_linestring_3d_local(strip_points_input)
        if not current_strip_shapely_3d:
            # print("DEBUG _segment_strip: Could not convert input strip to LineString.") # Optional
            return fill_segments_found

        if not all_3d_boundary_linestrings: # No boundaries to intersect with
            # print("DEBUG _segment_strip: No 3D boundaries provided. Returning original strip as fill.") # Optional
            fill_segments_found.append({'points': strip_points_input, 'normals': strip_normals_input})
            return fill_segments_found

        intersections_on_strip = [] # List to store (distance_along_strip, intersection_point_3d)
        for boundary_line_3d in all_3d_boundary_linestrings:
            if current_strip_shapely_3d.intersects(boundary_line_3d):
                intersection_geom = current_strip_shapely_3d.intersection(boundary_line_3d)
                
                def extract_points_from_geom_local(geom):
                    # (Same extract_points_from_geom logic as previously designed)
                    extracted = []
                    if geom.is_empty: return extracted
                    if geom.geom_type == 'Point': extracted.append(geom)
                    elif geom.geom_type == 'MultiPoint': extracted.extend(list(geom.geoms))
                    elif geom.geom_type == 'LineString':
                        coords = list(geom.coords)
                        if coords: extracted.append(Point(coords[0]));
                        if len(coords) > 1: extracted.append(Point(coords[-1]))
                    elif geom.geom_type == 'MultiLineString':
                        for ls_geom in geom.geoms:
                            coords = list(ls_geom.coords)
                            if coords: extracted.append(Point(coords[0]));
                            if len(coords) > 1: extracted.append(Point(coords[-1]))
                    elif geom.geom_type == 'GeometryCollection':
                        for sub_geom in geom.geoms: extracted.extend(extract_points_from_geom_local(sub_geom))
                    return extracted

                actual_intersection_points_shapely = extract_points_from_geom_local(intersection_geom)
                for int_point_shapely in actual_intersection_points_shapely:
                    distance_on_strip = current_strip_shapely_3d.project(int_point_shapely)
                    projected_on_strip_shapely = current_strip_shapely_3d.interpolate(distance_on_strip)
                    is_duplicate = any(abs(dist_existing - distance_on_strip) < 1e-5 for dist_existing, _ in intersections_on_strip)
                    if not is_duplicate:
                        intersections_on_strip.append((distance_on_strip, np.array(projected_on_strip_shapely.coords[0])))
        
        intersections_on_strip.sort(key=lambda x: x[0])

        if not intersections_on_strip:
            # print("DEBUG _segment_strip: Strip does not intersect any boundaries. Returning as single fill segment.") # Optional
            # Assumption: if a strip doesn't intersect, it's considered inside the "mesh face".
            fill_segments_found.append({'points': strip_points_input, 'normals': strip_normals_input})
            return fill_segments_found

        # print(f"DEBUG _segment_strip: Found {len(intersections_on_strip)} sorted intersections.") # Optional
        segmentation_points_3d = [strip_points_input[0]]
        for _, int_pt_3d in intersections_on_strip:
            segmentation_points_3d.append(int_pt_3d)
        if not np.allclose(strip_points_input[-1], segmentation_points_3d[-1]):
            segmentation_points_3d.append(strip_points_input[-1])

        refined_segment_pts_with_dist = []
        if current_strip_shapely_3d.length > 1e-6:
            for pt_3d in segmentation_points_3d:
                dist = current_strip_shapely_3d.project(Point(pt_3d))
                pt_on_line_exact = np.array(current_strip_shapely_3d.interpolate(dist).coords[0])
                if not any(abs(d_existing - dist) < 1e-5 for d_existing, _ in refined_segment_pts_with_dist):
                    refined_segment_pts_with_dist.append((dist, pt_on_line_exact))
        
        refined_segment_pts_with_dist.sort(key=lambda x: x[0])
        final_segmentation_points_3d = [pt for _, pt in refined_segment_pts_with_dist]

        is_fill_segment_type = True # First segment (strip_start to 1st_int) is FILL
        for i_seg_pt in range(len(final_segmentation_points_3d) - 1):
            p_start_sub = final_segmentation_points_3d[i_seg_pt]
            p_end_sub = final_segmentation_points_3d[i_seg_pt + 1]
            
            sub_segment_final_points = [p_start_sub]
            start_dist_sub = current_strip_shapely_3d.project(Point(p_start_sub))
            end_dist_sub = current_strip_shapely_3d.project(Point(p_end_sub))

            # Add original points from strip_points_input that fall strictly between p_start_sub and p_end_sub
            # This ensures we use the original interpolated points from the strip where possible.
            for k_orig_pt_idx in range(len(strip_points_input)):
                original_pt = strip_points_input[k_orig_pt_idx]
                if np.allclose(original_pt, p_start_sub) or np.allclose(original_pt, p_end_sub):
                    continue
                dist_orig_pt = current_strip_shapely_3d.project(Point(original_pt))
                # Check if dist_orig_pt is strictly between start_dist_sub and end_dist_sub with a small tolerance
                if (start_dist_sub < dist_orig_pt - 1e-6) and (dist_orig_pt < end_dist_sub - 1e-6):
                     # Further check: ensure this original point is not already too close to an intersection point
                     # to avoid including points that are essentially the intersection points themselves.
                    is_close_to_segmentation_pt = False
                    for seg_pt_check in final_segmentation_points_3d:
                        if np.allclose(original_pt, seg_pt_check, atol=1e-4):
                            is_close_to_segmentation_pt = True
                            break
                    if not is_close_to_segmentation_pt:
                        sub_segment_final_points.append(original_pt)
            
            if not np.allclose(p_end_sub, sub_segment_final_points[-1]):
                 sub_segment_final_points.append(p_end_sub)
            
            # Re-sort collected points for this sub-segment by their distance along the original strip
            # to ensure correct order before creating the final numpy array.
            sub_segment_points_with_dist_temp = []
            for pt_in_sub in sub_segment_final_points:
                dist_on_strip = current_strip_shapely_3d.project(Point(pt_in_sub))
                # Avoid duplicates based on distance again, very locally for this sub-segment
                if not any(abs(existing_d[0] - dist_on_strip) < 1e-5 for existing_d in sub_segment_points_with_dist_temp):
                    sub_segment_points_with_dist_temp.append((dist_on_strip, pt_in_sub))
            
            sub_segment_points_with_dist_temp.sort(key=lambda x: x[0])
            current_sub_segment_np = np.array([pt for _, pt in sub_segment_points_with_dist_temp])
            
            if len(current_sub_segment_np) >= self.min_points_req:
                if is_fill_segment_type:
                    # Get corresponding normals. This needs careful slicing from strip_normals_input
                    # or re-calculating if points were significantly altered.
                    # For now, we assume a direct mapping from original points or re-calculate.
                    # The safest is to re-calculate normals for the exact points in current_sub_segment_np.
                    sub_segment_normals = self.get_surface_normals_batch(current_sub_segment_np)
                    if sub_segment_normals is not None and len(sub_segment_normals) == len(current_sub_segment_np):
                        fill_segments_found.append({
                            'points': current_sub_segment_np,
                            'normals': sub_segment_normals
                        })
                        # print(f"DEBUG _segment_strip: Added FILL sub-segment with {len(current_sub_segment_np)} points.") # Optional
            is_fill_segment_type = not is_fill_segment_type
        
        # print(f"DEBUG _segment_strip: Returning {len(fill_segments_found)} fill segments.") # Optional
        return fill_segments_found

    def _trim_path_ends(self, path_points, trim_length):
        """
        Trims a specified length from both ends of a 3D path.
        
        Args:
            path_points (np.ndarray): The 3D points of the path strip.
            trim_length (float): The length to trim from each end.
            
        Returns:
            np.ndarray: The trimmed path points, or the original if too short to trim / becomes too short.
        """
        if path_points is None or len(path_points) < self.min_points_req or trim_length <= 1e-6:
            return path_points # Return original if no points, not enough points, or no trim length

        # Calculate cumulative lengths along the path
        segment_lengths = np.linalg.norm(np.diff(path_points, axis=0), axis=1)
        cumulative_lengths = np.concatenate(([0], np.cumsum(segment_lengths)))
        total_path_length = cumulative_lengths[-1]

        if total_path_length <= 2 * trim_length: # Path is too short to trim from both ends
            # Consider if it should return empty or original. For now, original.
            # If the requirement is strict that segments shorter than 2*trim_length are invalid after this,
            # then one might return np.array([]) here if total_path_length < self.min_points_req * some_min_segment_len
            return path_points 

        # --- Trim Start --- 
        points_after_start_trim_list = list(path_points)
        new_start_idx_offset = 0
        for i in range(len(cumulative_lengths)):
            if cumulative_lengths[i] >= trim_length:
                if i > 0: # Interpolate to find the new start point
                    p_b, p_a = path_points[i-1], path_points[i]
                    len_b, len_a = cumulative_lengths[i-1], cumulative_lengths[i]
                    seg_len = len_a - len_b
                    if seg_len > 1e-9:
                        t = (trim_length - len_b) / seg_len
                        t = np.clip(t, 0.0, 1.0)
                        new_start_pt = p_b + t * (p_a - p_b)
                        points_after_start_trim_list = [new_start_pt] + list(path_points[i:])
                        new_start_idx_offset = i # The original index from which points are kept (or new start point is before)
                    else: # Segment length is zero, effectively means points[i-1] and points[i] are same
                        points_after_start_trim_list = list(path_points[i:])
                        new_start_idx_offset = i
                else: # trim_length is 0 or very small, or first point itself is beyond trim_length
                    points_after_start_trim_list = list(path_points) # Keep all, effectively no trim from start
                    new_start_idx_offset = 0
                break
        else: # Loop finished without break, means path is shorter than trim_length from start
            return np.array([]) # Path is consumed by start trim

        current_trimmed_points = np.array(points_after_start_trim_list)
        if len(current_trimmed_points) < self.min_points_req:
             return np.array([]) # Became too short after start trim

        # --- Trim End --- 
        # Recalculate cumulative lengths for the (potentially start-trimmed) path
        segment_lengths_end = np.linalg.norm(np.diff(current_trimmed_points, axis=0), axis=1)
        cumulative_lengths_end = np.concatenate(([0], np.cumsum(segment_lengths_end)))
        total_path_length_after_start_trim = cumulative_lengths_end[-1]

        if total_path_length_after_start_trim <= trim_length: # Path is too short to trim from end (or consumed by start trim)
             # This implies the remaining path is shorter than or equal to one trim_length
             # If it was already start-trimmed, and now the remainder is <= trim_length,
             # it means the effective length is less than trim_length. So, empty.
            return np.array([]) 

        points_after_end_trim_list = list(current_trimmed_points)
        for i in range(len(cumulative_lengths_end) -1, -1, -1): # Iterate backwards
            length_from_end = total_path_length_after_start_trim - cumulative_lengths_end[i]
            if length_from_end >= trim_length:
                if i < len(cumulative_lengths_end) - 1: # Interpolate for new end point
                    # p_b is current_trimmed_points[i], p_a is current_trimmed_points[i+1]
                    # The new end point lies on the segment (points[i], points[i+1])
                    p_b_end, p_a_end = current_trimmed_points[i], current_trimmed_points[i+1]
                    # len_at_i_from_start = cumulative_lengths_end[i]
                    # len_at_i_plus_1_from_start = cumulative_lengths_end[i+1]
                    # We want a point on segment [p_b_end, p_a_end] such that its distance to total_path_length_after_start_trim is trim_length.
                    # Target length from start is total_path_length_after_start_trim - trim_length.
                    target_len_from_start_for_new_end = total_path_length_after_start_trim - trim_length
                    
                    seg_len_end = cumulative_lengths_end[i+1] - cumulative_lengths_end[i]
                    if seg_len_end > 1e-9:
                        t = (target_len_from_start_for_new_end - cumulative_lengths_end[i]) / seg_len_end
                        t = np.clip(t, 0.0, 1.0)
                        new_end_pt = p_b_end + t * (p_a_end - p_b_end)
                        points_after_end_trim_list = list(current_trimmed_points[:i+1]) + [new_end_pt]
                    else: # Segment length is zero
                        points_after_end_trim_list = list(current_trimmed_points[:i+1])
                else: # trim_length lands on or before the first point from end (i.e., current_trimmed_points[i])
                      # This case should ideally be handled by the total_path_length_after_start_trim <= trim_length check, 
                      # or if i is the last point, it means no trim from end based on this loop iteration.
                      # However, if i == len(cumulative_lengths_end) - 1, it implies the end point itself.
                      # For safety, if it reaches here, it implies the segment (current_trimmed_points[i], end) is being considered.
                      # If points[i] is the new end, then points_after_end_trim_list = list(current_trimmed_points[:i+1]) is correct.
                    points_after_end_trim_list = list(current_trimmed_points[:i+1]) 
                break
        else: # Loop finished without break, means path is shorter than trim_length from end
            return np.array([]) # Path is consumed by end trim
        
        final_trimmed_points = np.array(points_after_end_trim_list)
        if len(final_trimmed_points) < self.min_points_req:
            return np.array([]) # Became too short after end trim
            
        return final_trimmed_points

    def _get_smart_sample_indices(self, strip_points, num_samples):
        """
        智能采样索引生成：在条带的关键位置进行采样
        优先在条带的几何变化较大的位置采样
        """
        if len(strip_points) <= num_samples:
            return list(range(len(strip_points)))
        
        if num_samples <= 2:
            return [0, len(strip_points) - 1]
        
        # 计算每个点的曲率（基于相邻点的角度变化）
        curvatures = np.zeros(len(strip_points))
        
        for i in range(1, len(strip_points) - 1):
            v1 = strip_points[i] - strip_points[i-1]
            v2 = strip_points[i+1] - strip_points[i]
            
            # 计算角度变化作为曲率的代理
            v1_norm = np.linalg.norm(v1)
            v2_norm = np.linalg.norm(v2)
            
            if v1_norm > 1e-6 and v2_norm > 1e-6:
                cos_angle = np.clip(np.dot(v1, v2) / (v1_norm * v2_norm), -1.0, 1.0)
                curvatures[i] = 1.0 - cos_angle  # 曲率代理值
        
        # 始终包含端点
        selected_indices = [0, len(strip_points) - 1]
        remaining_samples = num_samples - 2
        
        if remaining_samples > 0:
            # 基于曲率和均匀分布的混合采样
            curvature_samples = max(1, int(remaining_samples // 2))  # 确保是整数
            uniform_samples = int(remaining_samples - curvature_samples)  # 确保是整数

            # 选择曲率最高的点
            if curvature_samples > 0:
                curvature_indices = np.argsort(curvatures)[-curvature_samples:]
                selected_indices.extend(curvature_indices.tolist())

            # 添加均匀分布的点
            if uniform_samples > 0:
                uniform_indices = np.linspace(1, len(strip_points) - 2, uniform_samples + 2)[1:-1].astype(int)
                selected_indices.extend(uniform_indices.tolist())
        
        # 去重并排序
        selected_indices = sorted(list(set(selected_indices)))
        return selected_indices[:num_samples]
    
    def _calculate_min_distance_to_strips_optimized(self, point, target_kdtree):
        """
        使用预构建的KDTree计算点到目标点云的最小距离。

        Args:
            point (numpy.ndarray): 查询的3D点 [x, y, z].
            target_kdtree (scipy.spatial.cKDTree): 从目标条带的点构建的预编译KDTree。

        Returns:
            float or None: 计算得到的最小距离。如果KDTree无效或查询失败，则返回None。
        """
        if target_kdtree is None:
            # self.logger.debug("_calculate_min_distance_to_strips_optimized: Provided target_kdtree is None.")
            return None

        try:
            # self.logger.debug(f"Querying pre-built KDTree for point: {point}")
            distance, _ = target_kdtree.query(point, k=1) # k=1 表示查找1个最近邻
        except Exception as e:
            # self.logger.error(f"Error during KDTree query in _calculate_min_distance_to_strips_optimized: {e}. Query point: {point}")
            return None
        
        # self.logger.debug(f"KDTree query result - distance: {distance}")
        return distance if distance is not None and not np.isnan(distance) else None

    def _filter_outliers_improved(self, distances):
        """
        改进的异常值过滤方法
        结合多种统计方法，提高过滤质量和速度
        """
        if len(distances) < 3:
            return distances
            
        distances_array = np.array(distances)
        
        # 第一步：使用快速的百分位数方法
        q25, q75 = np.percentile(distances_array, [25, 75])
        iqr = q75 - q25
        
        if iqr < 1e-6:  # 如果IQR太小，所有值都很接近
            return distances
        
        # 使用1.5倍IQR规则进行初步过滤
        lower_bound = q25 - 1.5 * iqr
        upper_bound = q75 + 1.5 * iqr
        
        # 基本IQR过滤
        iqr_mask = (distances_array >= lower_bound) & (distances_array <= upper_bound)
        iqr_filtered = distances_array[iqr_mask]
        
        # 如果IQR过滤结果太少，使用更宽松的标准
        if len(iqr_filtered) < max(3, len(distances) * 0.4):
            # 使用2.5倍IQR规则
            lower_bound = q25 - 2.5 * iqr
            upper_bound = q75 + 2.5 * iqr
            iqr_mask = (distances_array >= lower_bound) & (distances_array <= upper_bound)
            iqr_filtered = distances_array[iqr_mask]
        
        # 第二步：对IQR过滤结果进行Z-score精细过滤
        if len(iqr_filtered) >= 5:
            mean_filtered = np.mean(iqr_filtered)
            std_filtered = np.std(iqr_filtered)
            
            if std_filtered > 1e-6:
                z_scores = np.abs((iqr_filtered - mean_filtered) / std_filtered)
                final_mask = z_scores < 2.0  # 使用较严格的Z-score阈值
                final_filtered = iqr_filtered[final_mask]
                
                # 确保最终结果有足够的数据点
                if len(final_filtered) >= max(3, len(distances) * 0.3):
                    return final_filtered.tolist()
        
        # 如果精细过滤失败，返回IQR过滤结果
        return iqr_filtered.tolist() if len(iqr_filtered) > 0 else distances

    def _filter_outliers_fast(self, distances):
        """
        快速异常值过滤方法 - 性能优化版本
        使用简化的统计方法，减少计算复杂度
        """
        if len(distances) < 3:
            return distances
            
        distances_array = np.array(distances)
        
        # 使用简单的Z-score方法，比IQR更快
        mean_dist = np.mean(distances_array)
        std_dist = np.std(distances_array)
        
        if std_dist < 1e-6:  # 如果标准差太小，所有值都很接近
            return distances
        
        # 保留Z-score小于2.5的值（约99%的正常分布数据）
        z_scores = np.abs((distances_array - mean_dist) / std_dist)
        valid_mask = z_scores < 2.5
        
        filtered = distances_array[valid_mask]
        
        # 确保至少保留一些数据
        if len(filtered) < max(3, len(distances) * 0.3):
            # 如果过滤太严格，使用更宽松的标准
            valid_mask = z_scores < 3.0
            filtered = distances_array[valid_mask]
        
        return filtered.tolist() if len(filtered) > 0 else distances

    # ==================== ENHANCED ALGORITHMS ====================

    def _generate_spacing_cache_key(self, strips1, strips2, num_samples):
        """生成间距计算的缓存键 - 优化版本"""
        try:
            # 修正: 使用更宽松的精度和更简化的键生成策略
            key_parts = []

            # 简化键生成：只使用条带数量和大致位置
            key_parts.append(len(strips1))
            key_parts.append(len(strips2))

            # 对于每组条带，只使用第一个条带的起点和终点（降低精度）
            if strips1:
                strip_data = strips1[0]
                points = strip_data.get('points') if isinstance(strip_data, dict) else strip_data
                if isinstance(points, np.ndarray) and len(points) > 0:
                    # 使用更低精度（1位小数）减少键的变化
                    key_parts.extend([
                        round(points[0][0], 1), round(points[0][1], 1), round(points[0][2], 1),
                        round(points[-1][0], 1), round(points[-1][1], 1), round(points[-1][2], 1)
                    ])

            if strips2:
                strip_data = strips2[0]
                points = strip_data.get('points') if isinstance(strip_data, dict) else strip_data
                if isinstance(points, np.ndarray) and len(points) > 0:
                    key_parts.extend([
                        round(points[0][0], 1), round(points[0][1], 1), round(points[0][2], 1),
                        round(points[-1][0], 1), round(points[-1][1], 1), round(points[-1][2], 1)
                    ])

            # 将采样数量也降低精度
            key_parts.append(num_samples // 2 * 2)  # 取偶数
            return tuple(key_parts)
        except Exception as e:
            # 调试：记录缓存键生成失败的原因
            print(f"    调试: 缓存键生成失败: {e}")
            return None

    def _cache_spacing_result(self, cache_key, result):
        """缓存间距计算结果"""
        if cache_key is None:
            return

        # 限制缓存大小
        if len(self._spacing_cache) >= self._spacing_cache_max_size:
            # 移除最旧的条目（简单FIFO策略）
            oldest_key = next(iter(self._spacing_cache))
            del self._spacing_cache[oldest_key]

        self._spacing_cache[cache_key] = result

    def _calculate_polyline_length_fast(self, polyline):
        """快速计算多段线长度（向量化版本）"""
        if len(polyline) < 2:
            return 0.0

        # 向量化计算所有段长度
        segments = np.diff(polyline, axis=0)
        segment_lengths = np.linalg.norm(segments, axis=1)
        return np.sum(segment_lengths)

    def _enhanced_smart_sampling(self, strip_points, num_samples, target_spacing):
        """
        增强的智能采样策略
        基于几何特征和目标间距进行自适应采样
        """
        if len(strip_points) <= num_samples:
            return list(range(len(strip_points)))

        if num_samples <= 2:
            return [0, len(strip_points) - 1]

        # 计算曲率和密度权重
        curvatures = self._calculate_curvature_weights(strip_points)
        density_weights = self._calculate_density_weights(strip_points, target_spacing)

        # 组合权重
        combined_weights = 0.6 * curvatures + 0.4 * density_weights

        # 始终包含端点
        selected_indices = [0, len(strip_points) - 1]
        remaining_samples = num_samples - 2

        if remaining_samples > 0:
            # 基于权重选择采样点
            weighted_indices = np.argsort(combined_weights)[-remaining_samples:]
            selected_indices.extend(weighted_indices.tolist())

        # 去重并排序
        selected_indices = sorted(list(set(selected_indices)))
        return selected_indices[:num_samples]

    def _calculate_curvature_weights(self, points):
        """计算曲率权重"""
        curvatures = np.zeros(len(points))

        for i in range(1, len(points) - 1):
            v1 = points[i] - points[i-1]
            v2 = points[i+1] - points[i]

            v1_norm = np.linalg.norm(v1)
            v2_norm = np.linalg.norm(v2)

            if v1_norm > 1e-6 and v2_norm > 1e-6:
                cos_angle = np.clip(np.dot(v1, v2) / (v1_norm * v2_norm), -1.0, 1.0)
                curvatures[i] = 1.0 - cos_angle

        return curvatures

    def _calculate_density_weights(self, points, target_spacing):
        """基于目标间距计算密度权重"""
        weights = np.ones(len(points))

        # 计算点间距离
        if len(points) > 1:
            distances = np.linalg.norm(np.diff(points, axis=0), axis=1)

            # 在距离变化大的地方增加权重
            for i in range(1, len(distances)):
                distance_change = abs(distances[i] - distances[i-1])
                if distance_change > target_spacing * 0.2:
                    weights[i] += distance_change / target_spacing

        return weights

    def _estimate_surface_complexity(self):
        """
        估算表面复杂度，用于自适应算法参数调整
        返回0.0-1.0之间的复杂度因子
        """
        if self.mesh is None:
            return 0.5  # 默认中等复杂度

        try:
            # 计算网格的基本几何特征
            vertices = self.mesh.vertices
            faces = self.mesh.faces

            # 1. 计算曲率变化
            face_normals = self.mesh.face_normals
            if len(face_normals) > 1:
                # 计算相邻面法线的角度变化
                normal_changes = []
                for i in range(len(face_normals) - 1):
                    dot_product = np.clip(np.dot(face_normals[i], face_normals[i+1]), -1.0, 1.0)
                    angle_change = np.arccos(dot_product)
                    normal_changes.append(angle_change)

                avg_curvature = np.mean(normal_changes) if normal_changes else 0.0
                curvature_factor = min(avg_curvature / (np.pi / 4), 1.0)  # 归一化到0-1
            else:
                curvature_factor = 0.0

            # 2. 计算几何复杂度（基于面数和顶点数的比例）
            if len(vertices) > 0:
                face_vertex_ratio = len(faces) / len(vertices)
                geometry_factor = min(face_vertex_ratio / 2.0, 1.0)  # 归一化
            else:
                geometry_factor = 0.0

            # 3. 计算尺寸变化（基于边界框的纵横比）
            bounds = self.mesh.bounds
            dimensions = bounds[1] - bounds[0]
            if np.min(dimensions) > 0:
                aspect_ratio = np.max(dimensions) / np.min(dimensions)
                size_factor = min((aspect_ratio - 1.0) / 10.0, 1.0)  # 归一化
            else:
                size_factor = 0.0

            # 综合复杂度因子
            complexity = 0.4 * curvature_factor + 0.4 * geometry_factor + 0.2 * size_factor
            return np.clip(complexity, 0.0, 1.0)

        except Exception:
            return 0.5  # 出错时返回中等复杂度

    def _enhanced_prediction_algorithm(self, delta_y_history, spacing_error_history,
                                     target_spacing, learning_rate, momentum):
        """
        增强的步长预测算法
        结合混合求解器、线性回归、指数平滑、趋势分析
        优化版本：提高收敛速度和精度
        """
        if len(delta_y_history) == 0:
            return target_spacing

        if len(delta_y_history) == 1:
            # 简单的误差调整
            last_error = spacing_error_history[-1] if spacing_error_history else 0
            return delta_y_history[-1] - learning_rate * last_error

        # 初始化混合求解器（如果需要）
        if self._hybrid_solver is None:
            self._initialize_enhanced_solvers()

        # 尝试使用混合求解器进行优化预测
        if len(delta_y_history) >= 3 and len(spacing_error_history) >= 3:
            try:
                # 定义目标函数：最小化预测误差
                def prediction_error_func(delta_y):
                    if delta_y <= 0:
                        return float('inf')
                    # 基于历史数据的误差预测
                    recent_errors = spacing_error_history[-3:]
                    recent_deltas = delta_y_history[-3:]

                    # 线性插值预测误差
                    if len(recent_deltas) >= 2:
                        slope = (recent_errors[-1] - recent_errors[-2]) / (recent_deltas[-1] - recent_deltas[-2] + 1e-8)
                        predicted_error = recent_errors[-1] + slope * (delta_y - recent_deltas[-1])
                        return abs(predicted_error)
                    return abs(delta_y - target_spacing)

                # 使用混合求解器
                x0 = delta_y_history[-1]
                bounds = (target_spacing * 0.5, target_spacing * 2.0)

                result, success, iterations, method = self._hybrid_solver(
                    prediction_error_func, None, x0, bounds,
                    tolerance=target_spacing * 0.01, max_iterations=8
                )

                if success:
                    # 混合求解器成功，使用其结果
                    return result

            except Exception:
                pass  # 回退到传统方法

        # 方法1: 指数加权移动平均
        weights = np.exp(np.linspace(-2, 0, len(delta_y_history)))
        weights /= np.sum(weights)
        ewma_prediction = np.sum(np.array(delta_y_history) * weights)

        # 方法2: 线性趋势预测
        if len(delta_y_history) >= 3:
            x = np.arange(len(delta_y_history))
            y = np.array(delta_y_history)
            # 简单线性回归
            slope = np.sum((x - np.mean(x)) * (y - np.mean(y))) / np.sum((x - np.mean(x))**2)
            intercept = np.mean(y) - slope * np.mean(x)
            trend_prediction = slope * len(delta_y_history) + intercept
        else:
            trend_prediction = ewma_prediction

        # 方法3: 误差反馈调整
        if spacing_error_history:
            recent_errors = spacing_error_history[-3:] if len(spacing_error_history) >= 3 else spacing_error_history
            avg_error = np.mean(recent_errors)
            error_adjustment = learning_rate * avg_error
        else:
            error_adjustment = 0

        # 方法4: 动量调整
        if len(delta_y_history) >= 2:
            momentum_term = momentum * (delta_y_history[-1] - delta_y_history[-2])
        else:
            momentum_term = 0

        # 综合预测
        combined_prediction = (
            0.4 * ewma_prediction +
            0.3 * trend_prediction +
            0.2 * (delta_y_history[-1] - error_adjustment) +
            0.1 * momentum_term
        )

        return combined_prediction

    def _validate_and_correct_spacing(self, measured_spacing, target_spacing, tolerance_factor=0.07):
        """
        验证和修正间距测量结果 - 优化版本
        使用70%的目标阈值以提高间距质量，符合优化要求
        """
        if measured_spacing is None or measured_spacing <= 0:
            return target_spacing, False

        error_ratio = abs(measured_spacing - target_spacing) / target_spacing

        # 使用70%的目标阈值（从10%降到7%），更严格的质量控制
        if error_ratio > tolerance_factor:
            # 更精确的系统性偏差检测
            if measured_spacing < target_spacing * 0.85:  # 从0.8提高到0.85
                # 测量值过小，使用更保守的修正
                corrected_spacing = measured_spacing * 1.15  # 从1.2降到1.15
                print(f"    间距修正: {measured_spacing:.4f} -> {corrected_spacing:.4f}mm (测量值过小)")
                return corrected_spacing, True
            elif measured_spacing > target_spacing * 1.25:  # 从1.3降到1.25
                # 测量值过大，使用更保守的修正
                corrected_spacing = measured_spacing * 0.90  # 从0.85提高到0.90
                print(f"    间距修正: {measured_spacing:.4f} -> {corrected_spacing:.4f}mm (测量值过大)")
                return corrected_spacing, True

        return measured_spacing, False

    def _analyze_spacing_quality_comprehensive(self, spacing_analysis_data, target_3d_spacing):
        """
        综合间距质量分析 - 在所有3D间距计算完成后执行
        提供详细的质量指标和改进建议
        """
        if not spacing_analysis_data:
            print("    无间距数据可供分析")
            return {}

        # 提取有效的间距数据
        valid_spacings = []
        valid_errors = []
        spacing_warnings = 0

        for data in spacing_analysis_data:
            if not np.isnan(data['ActualAvg3DSpacing_mm']):
                valid_spacings.append(data['ActualAvg3DSpacing_mm'])
                valid_errors.append(data['Error_mm'])

                # 计算间距警告
                error_percentage = abs(data['Error_mm']) / target_3d_spacing * 100
                if error_percentage > 5:  # 超过5%误差的视为警告
                    spacing_warnings += 1

        if not valid_spacings:
            print("    无有效间距数据可供分析")
            return {}

        # 计算关键质量指标
        valid_spacings = np.array(valid_spacings)
        valid_errors = np.array(valid_errors)

        # 1. 目标间距达成率
        target_achievement_rate = np.mean(np.abs(valid_errors) <= target_3d_spacing * 0.05) * 100

        # 2. RMS误差
        rms_error = np.sqrt(np.mean(valid_errors**2))
        rms_error_percentage = (rms_error / target_3d_spacing) * 100

        # 3. 平均间距偏差
        avg_spacing = np.mean(valid_spacings)
        avg_deviation = abs(avg_spacing - target_3d_spacing)
        avg_deviation_percentage = (avg_deviation / target_3d_spacing) * 100

        # 4. 间距一致性（标准差）
        spacing_std = np.std(valid_spacings)
        consistency_score = max(0, 100 - (spacing_std / target_3d_spacing) * 100)

        # 5. 质量评分
        quality_score = (
            0.4 * target_achievement_rate +
            0.3 * max(0, 100 - rms_error_percentage * 2) +
            0.2 * max(0, 100 - avg_deviation_percentage * 3) +
            0.1 * consistency_score
        )

        # 输出详细分析结果
        print(f"\n  === 间距质量综合分析 ===")
        print(f"    目标3D间距: {target_3d_spacing:.4f}mm")
        print(f"    有效测量数: {len(valid_spacings)}")
        print(f"    间距警告数: {spacing_warnings}")
        print(f"    ")
        print(f"    关键指标:")
        print(f"      平均间距: {avg_spacing:.4f}mm (偏差: {avg_deviation_percentage:.1f}%)")
        print(f"      RMS误差: {rms_error:.4f}mm ({rms_error_percentage:.1f}%)")
        print(f"      目标达成率: {target_achievement_rate:.1f}% (±5%内)")
        print(f"      间距一致性: {consistency_score:.1f}%")
        print(f"      综合质量评分: {quality_score:.1f}%")

        # 质量评估和建议
        if quality_score >= 85:
            print(f"    质量评估: 优秀 ✓")
        elif quality_score >= 70:
            print(f"    质量评估: 良好")
        elif quality_score >= 50:
            print(f"    质量评估: 一般，需要改进")
        else:
            print(f"    质量评估: 较差，需要大幅改进")

        # 目标达成情况检查
        target_checks = {
            'spacing_within_5pct': target_achievement_rate >= 95,
            'rms_error_under_15pct': rms_error_percentage < 15,
            'spacing_warnings_under_5': spacing_warnings < 5
        }

        print(f"    ")
        print(f"    目标达成情况:")
        print(f"      间距±5%内: {'✓' if target_checks['spacing_within_5pct'] else '✗'} ({target_achievement_rate:.1f}% >= 95%)")
        print(f"      RMS误差<15%: {'✓' if target_checks['rms_error_under_15pct'] else '✗'} ({rms_error_percentage:.1f}% < 15%)")
        print(f"      间距警告<5: {'✓' if target_checks['spacing_warnings_under_5'] else '✗'} ({spacing_warnings} < 5)")

        return {
            'quality_score': quality_score,
            'target_achievement_rate': target_achievement_rate,
            'rms_error_percentage': rms_error_percentage,
            'spacing_warnings': spacing_warnings,
            'avg_deviation_percentage': avg_deviation_percentage,
            'consistency_score': consistency_score,
            'target_checks': target_checks
        }

    def _adaptive_tolerance_adjustment(self, error_history, base_tolerance):
        """
        基于误差历史自适应调整容差
        """
        if len(error_history) < 3:
            return base_tolerance

        recent_errors = error_history[-3:]
        avg_error = np.mean(recent_errors)
        error_variance = np.var(recent_errors)

        # 如果误差稳定且较小，可以收紧容差
        if error_variance < base_tolerance * 0.1 and avg_error < base_tolerance * 0.5:
            return base_tolerance * 0.8
        # 如果误差波动较大，放宽容差
        elif error_variance > base_tolerance * 0.5:
            return base_tolerance * 1.3
        else:
            return base_tolerance


if __name__ == "__main__":
    # 确保 time 在此作用域内可用，尽管已经在顶部导入
    # 但为了 edit_file 的明确性，可以再次引用或假设其已在全局作用域
    script_total_start_time_main = time.time() # 记录脚本总开始时间

    # 定义要处理的多个网格文件列表
    input_mesh_files_list = [
        # "src/m19_mnp_003.stl", # 首先处理这个
        # "src/m19_mnp_002.stl", # 
        # "src/m19_mnp_001.stl", # 
        # "src/xiekua_mnp_003.stl",  # 然后处理这个
        # "src/xiekua_mnp_002.stl",  # 然后处理这个
        # "src/xiekua_mnp_001.stl",  # 然后处理这个
        # "src/m11_mnp_001.stl",
        # "src/m11_mnp_000.stl",
        # "src\m21_mnp_003.stl" ,# 然后处理这个
        # "src\m21_mnp_002.stl", # 然后处理这个
        "src\m21_mnp_001.stl", # 然后处理这个
        # "src/jiyi_mnp_002.stl", # 然后处理这个
        # "src/jiyi_mnp_001.stl", # 然后处理这个
        # "src/mengpi_mnp_000.stl", # 然后处理这个

        

        


    ]
    
    # --- G-code 模式选择 ---
    enable_rotation_axes = False  # True: 6轴G-code模式(包含ABC角度), False: 传统G-code模式(仅XYZ)
    
    output_paths_filename = "direct_projected_paths.txt" # 输出路径文本文件名
    
    # 根据模式选择输出文件名
    if enable_rotation_axes:
        output_gcode_filename = "direct_projected_paths_6axis.gcode" # 6轴G-code文件名
        print(f"--- Mode Selected: 6-Axis G-code Mode (with ABC rotation axes) ---")
    else:
        output_gcode_filename = "direct_projected_paths_traditional.gcode" # 传统G-code文件名
        print(f"--- Mode Selected: Traditional G-code Mode (XYZ coordinates only) ---")
    
    script_dir = os.path.dirname(os.path.abspath(__file__)) #脚本所在目录
    output_paths_file_abs = os.path.join(script_dir, output_paths_filename) # 输出路径文件的绝对路径
    output_gcode_file_abs = os.path.join(script_dir, output_gcode_filename) # 输出G-code文件的绝对路径
    
    target_bead_width = 0.6
    # G-code 生成参数
    gcode_target_extruder_temp = 220        # 挤出机目标温度 (°C) - 匹配正常文件
    gcode_target_bed_temp = 60              # 热床目标温度 (°C)
    gcode_filament_diameter = 1.75          # 耗材直径 (mm)
    gcode_nozzle_diameter = 0.6             # 喷嘴直径 (mm)
    gcode_extrusion_width = 0.4             # 挤出宽度 (mm) - 匹配正常文件
    gcode_layer_height = 0.4               # 层高 (mm) - 匹配正常文件
    gcode_retraction_amount = 0.8           # 回抽量 (mm) - 匹配正常文件
    gcode_retraction_feedrate = 1800        # 回抽速度 (mm/min) - 匹配正常文件
    gcode_print_feedrate = 3000             # 打印速度 (mm/min)
    gcode_travel_feedrate = 21000           # 空程速度 (mm/min) - 匹配正常文件
    gcode_extrusion_multiplier = 1.1       # 挤出倍率 (1.0 = 100%)
    gcode_slope_compensation_exponent = 0   # 斜率补偿指数 (0表示无补偿, 1表示1/cos(theta)补偿)

    gcode_normal_hop_distance = 2.0         # 法线方向跳跃距离 (mm)
    gcode_clearance_above_model_max = 3.0   # 模型上方最大间隙 (mm)
    gcode_rotation_feedrate = 5000          # 旋转轴进给速率 (mm/min or deg/min, 取决于固件)

    # --- 路径生成策略和参数 ---
    strategy_choice = 'direct_offset' # 选择 'projection' (投影) 策略
    # strategy_choice = 'direct_offset' # 注释掉 direct_offset

    # 投影策略的填充模式
    proj_fill_pattern_param = 'raster' # 可选 'raster' 或 'concentric'

    # 通用参数，还有个proximity_threshold = 0.5 # 毫米，点与边界的最小允许距离
    # 对于投影策略: 2D填充线间距。对于直接偏置策略: 3D条带间距。
    path_row_spacing = target_bead_width * 0.7
    # 用于插值的最大段长。
    path_max_segment_length = target_bead_width * 2 # Changed from target_bead_width * 2.0
    
    # 为投影策略单独设置最大段长参数
    projection_max_segment_length_param = target_bead_width * 1 # 投影策略使用更小的插值段长
    
    # 参数 'offset_distance_param' 的含义取决于策略:
    # 对于 'projection': 它是2D多边形填充区域的向内偏移。
    # 对于 'direct_offset': 它是沿偏置轴从网格边界的边距。
    offset_distance_param = target_bead_width / 2.0

    # 切片方向 (影响两种策略的X/Y轴选择)
    slicer_slice_direction = "y" # 'x' 或 'y'.对于直接偏置，这是我们步进的轴。
    
    # 边界和孔洞距离参数 (毫米) - 控制填充路径与边界/孔洞的最小距离
    proximity_threshold_param = 1  # 可调整此值来控制与边界和孔洞的距离

    # 特定于 'direct_offset' (直接偏置) 策略的自适应密度控制参数
    adaptive_density_enabled = True       # 启用自适应密度控制
         

    # 革命性优化参数：完全重新设计以达到性能目标
    # 目标：≥95%目标达成率，<15% RMS误差，>85%质量评分，>20%缓存命中率，<3间距警告
    iter_min_delta_y_factor_param = 0.8      # 大幅提高最小步长，避免过度保守
    iter_max_delta_y_factor_param = 1.15     # 适度限制最大步长，保持稳定性
    iter_tolerance_abs_param = 0.02          # 放宽容差，避免过度严格导致的收敛失败
    iter_max_iterations_per_step_param = 8   # 减少迭代次数，避免过度优化
    iter_num_samples_for_spacing_calc_param = 12  # 增加采样数，提高测量精度

    print(f"--- 多文件路径生成参数 ---")
    print(f"  输入网格文件数量: {len(input_mesh_files_list)}")
    for i, mesh_file in enumerate(input_mesh_files_list, 1):
        print(f"  文件 {i}: {mesh_file}")
    print(f"  生成策略: {strategy_choice}")
    print(f"  切片/偏置方向轴: {slicer_slice_direction.upper()}")
    print(f"  目标珠宽: {target_bead_width:.3f} mm")
    print(f"  路径行距/条带间距: {path_row_spacing:.3f} mm")
    # print(f"  路径最大段长(插值用): {path_max_segment_length:.3f} mm") # 旧的通用打印方式
    if strategy_choice == 'direct_offset':
        print(f"  直接偏置 - 路径最大段长(插值用): {path_max_segment_length:.3f} mm")
    elif strategy_choice == 'projection':
        print(f"  投影 - 路径最大段长(插值用): {projection_max_segment_length_param:.3f} mm")
    print(f"  边界和孔洞距离阈值: {proximity_threshold_param:.3f} mm")
    if strategy_choice == 'direct_offset':
        print(f"  直接偏置策略 - 边界内缩量: {offset_distance_param:.3f} mm")
        print(f"  直接偏置策略 - 自适应密度: {'启用' if adaptive_density_enabled else '禁用'}")
        if adaptive_density_enabled:
            # 日志现在将只反映迭代自适应方法
            print(f"  直接偏置策略 - 使用迭代反馈自适应间距方法")
            print(f"    迭代参数: MinΔYFactor={iter_min_delta_y_factor_param:.2f}, MaxΔYFactor={iter_max_delta_y_factor_param:.2f}, AbsTol={iter_tolerance_abs_param:.2f}mm")
    elif strategy_choice == 'projection':
        print(f"  投影策略 - 2D边界内缩量: {offset_distance_param:.3f} mm")
        print(f"  投影策略 - 2D填充模式: {proj_fill_pattern_param}")
    print(f"-------------------------")

    # 存储所有文件的路径数据
    all_files_paths = []
    total_segments_count = 0
    
    print("\n--- 开始处理多个文件的路径生成过程 ---")
    slicing_start_time = time.time()
    
    # 处理每个文件
    for file_index, input_mesh_file in enumerate(input_mesh_files_list, 1):
        print(f"\n=== 正在处理文件 {file_index}/{len(input_mesh_files_list)}: {input_mesh_file} ===")
        
        # 检查文件是否存在
        if not os.path.exists(input_mesh_file):
            print(f"警告: 文件 {input_mesh_file} 不存在，跳过处理。")
            continue
            
        try:
            # 为当前文件创建切片器实例
            projector = DirectProjectionSlicer(
                mesh_path=input_mesh_file,
                target_surface_distance=target_bead_width, # 参考值，direct_offset策略不直接用于路径生成
                slice_direction=slicer_slice_direction,    # 切片/偏置方向
                inward_normals=True, # 法线朝内
                min_points_req=2      # 路径段所需最小点数
            )

            # 根据策略选择合适的max_segment_length
            current_max_segment_length = projection_max_segment_length_param if strategy_choice == 'projection' else path_max_segment_length

            # 生成当前文件的路径
            current_file_paths = projector.create_projected_fill_paths(
                row_spacing=path_row_spacing,
                offset_distance=offset_distance_param, 
                max_segment_length=current_max_segment_length, # 使用条件选择的max_segment_length
                strategy=strategy_choice,
                projection_fill_pattern=proj_fill_pattern_param, # 为投影策略传递填充模式
                proximity_threshold=proximity_threshold_param, 
                adaptive_density=adaptive_density_enabled, 
                # min_spacing_factor=min_spacing_factor_param, # Removed old param
                # use_normal_component_method=use_normal_component_method_param, # Removed old param
                # ny_calculation_strategy=ny_calculation_strategy_param, # Removed old param
                # Pass new iteration parameters from main to create_projected_fill_paths
                iter_min_delta_y_factor=iter_min_delta_y_factor_param,
                iter_max_delta_y_factor=iter_max_delta_y_factor_param,
                iter_tolerance_abs=iter_tolerance_abs_param,
                iter_max_iterations_per_step=iter_max_iterations_per_step_param,
                iter_num_samples_for_spacing_calc=iter_num_samples_for_spacing_calc_param
            )
            
            if current_file_paths: # current_file_paths is now a tuple (paths_list, spacing_data)
                paths_list_for_file = current_file_paths[0]
                spacing_data_for_file = current_file_paths[1]

                print(f"文件 {input_mesh_file} 成功生成 {len(paths_list_for_file)} 个路径段")
                
                # 为当前文件的路径段添加偏移量，避免segment_id冲突
                adjusted_paths = []
                segment_id_offset = file_index * 100000  # 每个文件使用不同的ID范围
                
                for points, normals, is_boundary, segment_id in paths_list_for_file:
                    # 调整segment_id以避免冲突
                    new_segment_id = segment_id + segment_id_offset
                    adjusted_paths.append((points, normals, is_boundary, new_segment_id))
                
                all_files_paths.extend(adjusted_paths)
                total_segments_count += len(paths_list_for_file)
                
                print(f"文件 {input_mesh_file} 的路径已添加到总集合中")

                # 保存当前文件的间距分析数据
                if spacing_data_for_file:
                    base_name, _ = os.path.splitext(os.path.basename(input_mesh_file))
                    spacing_output_filename = f"spacing_analysis_{base_name}.csv"
                    spacing_output_file_abs = os.path.join(script_dir, spacing_output_filename)
                    projector.save_spacing_analysis_to_file(spacing_data_for_file, spacing_output_file_abs)
                else:
                    print(f"文件 {input_mesh_file} 没有生成间距分析数据。")
                
            else: # current_file_paths was ([], []) or something indicating failure
                print(f"警告: 文件 {input_mesh_file} 未能生成任何路径段或间距数据")
                
        except Exception as e:
            print(f"错误: 处理文件 {input_mesh_file} 时发生异常: {type(e).__name__} - {e!r}")
            print("详细错误信息:")
            traceback.print_exc() # 打印完整的堆栈跟踪
            continue
    
    slicing_duration = time.time() - slicing_start_time
    print(f"--- 所有文件路径生成总耗时: {slicing_duration:.4f} 秒 ---")
    print(f"--- 总计处理了 {len(input_mesh_files_list)} 个文件，生成了 {total_segments_count} 个路径段 ---")

    if all_files_paths:
        print(f"\n成功合并所有文件的路径，总共 {len(all_files_paths)} 个路径段。正在保存和可视化...")
        
        # 创建一个代表性的切片器实例用于可视化和G-code生成（使用第一个有效文件）
        representative_projector = None
        for input_mesh_file in input_mesh_files_list:
            if os.path.exists(input_mesh_file):
                try:
                    representative_projector = DirectProjectionSlicer(
                        mesh_path=input_mesh_file,
                        target_surface_distance=target_bead_width,
                        slice_direction=slicer_slice_direction,
                        inward_normals=True,
                        min_points_req=2
                    )
                    break
                except:
                    continue
        
        if representative_projector is None:
            print("错误: 无法创建代表性切片器实例用于可视化和G-code生成")
        else:
            # 可视化合并后的路径
            representative_projector.visualize_paths(all_files_paths, 
                                      show_normals=False, # 设置为True以查看法线
                                      normal_scale=target_bead_width * 0.5, # 法线可视化长度
                                      normal_hop_distance=gcode_normal_hop_distance, # 与G-code参数匹配以进行可视化
                                      clearance_above_model_max_viz=gcode_clearance_above_model_max # 同上
                                      )
            
            print("\n--- 开始合并后的G-code保存过程 ---")
            gcode_gen_start_time = time.time()
            representative_projector.save_paths_to_gcode(all_files_paths, output_gcode_file_abs, 
                                          klipper_mode=True, # 改为Klipper模式以匹配正常文件的M83相对挤出
                                          feed_rate_print=gcode_print_feedrate,
                                          feed_rate_travel=gcode_travel_feedrate,
                                          target_extruder_temp=gcode_target_extruder_temp,
                                          target_bed_temp=gcode_target_bed_temp,
                                          filament_diameter=gcode_filament_diameter,
                                          extrusion_width=gcode_extrusion_width, 
                                          layer_height=gcode_layer_height,    
                                          retraction_amount=gcode_retraction_amount,
                                          retraction_feedrate=gcode_retraction_feedrate,
                                          extrusion_multiplier=gcode_extrusion_multiplier,
                                          normal_hop_distance=gcode_normal_hop_distance, 
                                          clearance_above_model_max=gcode_clearance_above_model_max, 
                                          rotation_feed_rate=gcode_rotation_feedrate, 
                                          enable_rotation_axes=enable_rotation_axes
                                          ) 
            gcode_gen_duration = time.time() - gcode_gen_start_time
            print(f"--- 合并后的G-code 保存总耗时: {gcode_gen_duration:.4f} 秒 ---")
            print(f"--- 合并后的G-code文件已保存到: {output_gcode_file_abs} ---")
    else:
        print("未能从任何文件生成路径。")