; G-code Generated by DirectProjectionSlicer (Advanced Hop Logic)
; Mode: Traditional G-code Mode (XYZ coordinates only)
; Original Mesh: src\m21_mnp_001.stl
; XY offset to center model approx at (100,100). Original Z values are maintained.\n; Applied X_offset: -871.2552, Y_offset: -73.6971, Z_offset: 0.0000
; Normal Hop Distance: 2.00mm, Max Clearance Above Model: 3.00mm (Target Z for hop: 69.4895)
; Filament Diameter: 1.75mm, Extrusion Width: 0.40mm, Layer Height: 0.40mm
; Retraction: 0.80mm @ 1800mm/min
; Extrusion Multiplier: 110.0%
; Klipper Mode Activated
G21 ; Set units to millimeters
G90 ; Use absolute XYZ positioning
M83 ; Use relative E distances (safer for Klipper extrusion calculation with SET_EXTRUDER_ROTATION_DISTANCE)
PRINT_START BED_TEMP=60 EXTRUDER_TEMP=220
G0 X0.0000 Y0.0000 Z74.4895 F21000 ; Initial position (Traditional mode)
; -- Path Data Start --

; ---- Preparing Path Segment 1, OriginalID: 100001, Type: BOUNDARY, Points: 21 ----
G0 X76.5711 Y64.8450 Z74.4895 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X76.5711 Y64.8450 Z64.7043 F21000 ; Lower to Pre-Approach Z
G0 F5000 ; Reorient at Pre-Approach Point (Traditional mode)
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 1
G1 X77.1187 Y65.5986 Z62.9344 F3000
G1 X88.4243 Y64.3875 Z65.3642 E0.85078 F3000
G1 X100.0000 Y63.9797 Z66.1789 E0.84964 F3000
G1 X111.5757 Y64.3875 Z65.3642 E0.84964 F3000
G1 X122.8813 Y65.5986 Z62.9344 E0.85078 F3000
G1 X127.5270 Y75.9767 Z65.3642 E0.85079 F3000
G1 X131.4918 Y86.8598 Z66.1789 E0.84964 F3000
G1 X134.6811 Y97.9949 Z65.3642 E0.84964 F3000
G1 X137.0229 Y109.1216 Z62.9344 E0.85078 F3000
G1 X128.5883 Y116.7468 Z65.3642 E0.85078 F3000
G1 X119.4630 Y123.8806 Z66.1789 E0.84964 F3000
G1 X109.8585 Y130.3548 Z65.3642 E0.84963 F3000
G1 X100.0000 Y136.0203 Z62.9344 E0.85079 F3000
G1 X90.1415 Y130.3548 Z65.3642 E0.85079 F3000
G1 X80.5370 Y123.8806 Z66.1789 E0.84963 F3000
G1 X71.4117 Y116.7468 Z65.3642 E0.84964 F3000
G1 X62.9771 Y109.1216 Z62.9344 E0.85079 F3000
G1 X65.3189 Y97.9949 Z65.3642 E0.85078 F3000
G1 X68.5082 Y86.8598 Z66.1789 E0.84964 F3000
G1 X72.4731 Y75.9767 Z65.3642 E0.84964 F3000
G1 X77.1187 Y65.5986 Z62.9344 E0.85078 F3000
; Path Segment 1 Print End
; Starting Advanced Hop from end of Segment 100001
G1 E-0.80000 F1800 ; Retract before hop (Klipper M83)
G1 X76.5711 Y64.8450 Z64.7043 F21000 ; Hop along tool axis normal (Traditional mode)
G0 F5000 ; Return tool to vertical (Traditional mode)
G0 Z69.4895 F21000 ; Z-axis secondary hop to absolute safe height
; Advanced hop sequence for segment 100001 complete. Preparing approach for next segment.

; ---- Preparing Path Segment 2, OriginalID: 90000, Type: FILL_SEGMENT_90000, Points: 16 ----
G0 X91.4682 Y63.4916 Z69.4895 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X91.4682 Y63.4916 Z67.4166 F21000 ; Lower to Pre-Approach Z
G0 F5000 ; Reorient at Pre-Approach Point (Traditional mode)
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 2
G1 X91.7814 Y64.2797 Z65.6053 F3000
G1 X92.6676 Y64.2797 Z65.6821 E0.06509 F3000
G1 X93.8528 Y64.2797 Z65.7847 E0.08704 F3000
G1 X95.0379 Y64.2797 Z65.8873 E0.08704 F3000
G1 X96.2230 Y64.2797 Z65.9898 E0.08704 F3000
G1 X97.4081 Y64.2797 Z66.0924 E0.08704 F3000
G1 X98.5933 Y64.2797 Z66.1950 E0.08704 F3000
G1 X99.7784 Y64.2797 Z66.2976 E0.08704 F3000
G1 X100.2216 Y64.2797 Z66.2976 E0.03243 F3000
G1 X101.4067 Y64.2797 Z66.1950 E0.08704 F3000
G1 X102.5919 Y64.2797 Z66.0924 E0.08704 F3000
G1 X103.7770 Y64.2797 Z65.9898 E0.08704 F3000
G1 X104.9621 Y64.2797 Z65.8873 E0.08704 F3000
G1 X106.1472 Y64.2797 Z65.7847 E0.08704 F3000
G1 X107.3324 Y64.2797 Z65.6821 E0.08704 F3000
G1 X108.2186 Y64.2797 Z65.6053 E0.06509 F3000
; Path Segment 2 Print End
; Starting Advanced Hop from end of Segment 90000
G1 E-0.80000 F1800 ; Retract before hop (Klipper M83)
G1 X108.5318 Y63.4916 Z67.4166 F21000 ; Hop along tool axis normal (Traditional mode)
G0 F5000 ; Return tool to vertical (Traditional mode)
G0 Z69.4895 F21000 ; Z-axis secondary hop to absolute safe height
; Advanced hop sequence for segment 90000 complete. Preparing approach for next segment.

; ---- Preparing Path Segment 3, OriginalID: 89999, Type: FILL_SEGMENT_89999, Points: 28 ----
G0 X112.9284 Y63.7417 Z69.4895 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X112.9284 Y63.7417 Z66.9664 F21000 ; Lower to Pre-Approach Z
G0 F5000 ; Reorient at Pre-Approach Point (Traditional mode)
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 3
G1 X112.6152 Y64.5299 Z65.1551 F3000
G1 X112.2902 Y64.5299 Z65.2409 E0.02460 F3000
G1 X111.6751 Y64.5299 Z65.4033 E0.04655 F3000
G1 X111.4729 Y64.5299 Z65.4386 E0.01501 F3000
G1 X110.3663 Y64.5299 Z65.5344 E0.08128 F3000
G1 X109.2596 Y64.5299 Z65.6302 E0.08128 F3000
G1 X108.1530 Y64.5299 Z65.7260 E0.08128 F3000
G1 X107.0463 Y64.5299 Z65.8218 E0.08128 F3000
G1 X105.9397 Y64.5299 Z65.9176 E0.08128 F3000
G1 X104.8330 Y64.5299 Z66.0134 E0.08128 F3000
G1 X103.7264 Y64.5299 Z66.1092 E0.08128 F3000
G1 X102.6197 Y64.5299 Z66.2050 E0.08128 F3000
G1 X101.5131 Y64.5299 Z66.3008 E0.08128 F3000
G1 X100.4064 Y64.5299 Z66.3966 E0.08128 F3000
G1 X99.5936 Y64.5299 Z66.3966 E0.05948 F3000
G1 X98.4869 Y64.5299 Z66.3008 E0.08128 F3000
G1 X97.3803 Y64.5299 Z66.2050 E0.08128 F3000
G1 X96.2736 Y64.5299 Z66.1092 E0.08128 F3000
G1 X95.1670 Y64.5299 Z66.0134 E0.08128 F3000
G1 X94.0603 Y64.5299 Z65.9176 E0.08128 F3000
G1 X92.9537 Y64.5299 Z65.8218 E0.08128 F3000
G1 X91.8470 Y64.5299 Z65.7260 E0.08128 F3000
G1 X90.7404 Y64.5299 Z65.6302 E0.08128 F3000
G1 X89.6337 Y64.5299 Z65.5344 E0.08128 F3000
G1 X88.5271 Y64.5299 Z65.4386 E0.08128 F3000
G1 X88.3249 Y64.5299 Z65.4033 E0.01501 F3000
G1 X87.7098 Y64.5299 Z65.2409 E0.04655 F3000
G1 X87.3848 Y64.5299 Z65.1551 E0.02460 F3000
; Path Segment 3 Print End
; Starting Advanced Hop from end of Segment 89999
G1 E-0.80000 F1800 ; Retract before hop (Klipper M83)
G1 X87.0716 Y63.7417 Z66.9664 F21000 ; Hop along tool axis normal (Traditional mode)
G0 F5000 ; Return tool to vertical (Traditional mode)
G0 Z69.4895 F21000 ; Z-axis secondary hop to absolute safe height
; Advanced hop sequence for segment 89999 complete. Preparing approach for next segment.

; ---- Preparing Path Segment 4, OriginalID: 89998, Type: FILL_SEGMENT_89998, Points: 28 ----
G0 X84.8812 Y63.9764 Z69.4895 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X84.8812 Y63.9764 Z66.4956 F21000 ; Lower to Pre-Approach Z
G0 F5000 ; Reorient at Pre-Approach Point (Traditional mode)
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 4
G1 X85.1944 Y64.7645 Z64.6843 F3000
G1 X85.9899 Y64.7645 Z64.8944 E0.06021 F3000
G1 X87.0756 Y64.7645 Z65.1811 E0.08216 F3000
G1 X88.1612 Y64.7645 Z65.4677 E0.08216 F3000
G1 X88.6964 Y64.7645 Z65.5611 E0.03975 F3000
G1 X89.8879 Y64.7645 Z65.6643 E0.08751 F3000
G1 X91.0794 Y64.7645 Z65.7674 E0.08751 F3000
G1 X92.2710 Y64.7645 Z65.8706 E0.08751 F3000
G1 X93.4625 Y64.7645 Z65.9737 E0.08751 F3000
G1 X94.6541 Y64.7645 Z66.0769 E0.08751 F3000
G1 X95.8456 Y64.7645 Z66.1800 E0.08751 F3000
G1 X97.0372 Y64.7645 Z66.2832 E0.08751 F3000
G1 X98.2287 Y64.7645 Z66.3863 E0.08751 F3000
G1 X99.4202 Y64.7645 Z66.4895 E0.08751 F3000
G1 X100.5798 Y64.7645 Z66.4895 E0.08485 F3000
G1 X101.7713 Y64.7645 Z66.3863 E0.08751 F3000
G1 X102.9629 Y64.7645 Z66.2832 E0.08751 F3000
G1 X104.1544 Y64.7645 Z66.1800 E0.08751 F3000
G1 X105.3459 Y64.7645 Z66.0769 E0.08751 F3000
G1 X106.5375 Y64.7645 Z65.9737 E0.08751 F3000
G1 X107.7290 Y64.7645 Z65.8706 E0.08751 F3000
G1 X108.9206 Y64.7645 Z65.7674 E0.08751 F3000
G1 X110.1121 Y64.7645 Z65.6643 E0.08751 F3000
G1 X111.3037 Y64.7645 Z65.5611 E0.08751 F3000
G1 X111.8388 Y64.7645 Z65.4677 E0.03975 F3000
G1 X112.9244 Y64.7645 Z65.1811 E0.08216 F3000
G1 X114.0101 Y64.7645 Z64.8944 E0.08216 F3000
G1 X114.8056 Y64.7645 Z64.6843 E0.06021 F3000
; Path Segment 4 Print End
; Starting Advanced Hop from end of Segment 89998
G1 E-0.80000 F1800 ; Retract before hop (Klipper M83)
G1 X115.1188 Y63.9764 Z66.4956 F21000 ; Hop along tool axis normal (Traditional mode)
G0 F5000 ; Return tool to vertical (Traditional mode)
G0 Z69.4895 F21000 ; Z-axis secondary hop to absolute safe height
; Advanced hop sequence for segment 89998 complete. Preparing approach for next segment.

; ---- Preparing Path Segment 5, OriginalID: 89997, Type: FILL_SEGMENT_89997, Points: 3 ----
G0 X100.2225 Y136.6518 Z69.4895 F21000 ; Move to Pre-Approach XY (Safe Z)
G0 X100.2225 Y136.6518 Z64.8497 F21000 ; Lower to Pre-Approach Z
G0 F5000 ; Reorient at Pre-Approach Point (Traditional mode)
G1 E0.80000 F1800 ; Prime (Klipper M83)
; Printing Path Segment 5
G1 X100.2225 Y135.7203 Z63.0799 F3000
G1 X100.0000 Y135.7203 Z63.0923 E0.01631 F3000
G1 X99.7775 Y135.7203 Z63.0799 E0.01631 F3000
; Path Segment 5 Print End
; Starting Advanced Hop from end of Segment 89997
G1 E-0.80000 F1800 ; Retract before hop (Klipper M83)
G1 X99.7775 Y136.6518 Z64.8497 F21000 ; Hop along tool axis normal (Traditional mode)
G0 F5000 ; Return tool to vertical (Traditional mode)
G0 Z69.4895 F21000 ; Z-axis secondary hop to absolute safe height
; Advanced hop sequence for segment 89997 complete. Preparing approach for next segment.

; -- Path Data End --
G1 E-0.80000 F1800 ; Final Retraction (Klipper M83)
PRINT_END ; Call Klipper PRINT_END macro
